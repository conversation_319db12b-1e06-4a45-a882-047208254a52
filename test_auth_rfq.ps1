# Test Authentication and RFQ Creation
Write-Host "Testing Authentication and RFQ Creation..." -ForegroundColor Yellow

# Test 1: Check backend status
Write-Host "`n1. Checking backend status..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/notifications/vapid-keys" -Method GET -TimeoutSec 10
    Write-Host "Backend is running" -ForegroundColor Green
} catch {
    Write-Host "Backend is not running" -ForegroundColor Red
    exit 1
}

# Test 2: Test login
Write-Host "`n2. Testing login..." -ForegroundColor Cyan
try {
    $loginData = @{
        email = "<EMAIL>"
        password = "password123"
    }
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5001/auth/login" -Method POST -Headers @{
        "Content-Type" = "application/json"
    } -Body (ConvertTo-Json $loginData) -TimeoutSec 10
    
    Write-Host "Login successful" -ForegroundColor Green
    Write-Host "Token received: $($loginResponse.token.Substring(0, 20))..." -ForegroundColor Gray
    Write-Host "User role: $($loginResponse.user.role)" -ForegroundColor Gray
    
    $token = $loginResponse.token
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Test RFQ creation
Write-Host "`n3. Testing RFQ creation..." -ForegroundColor Cyan
try {
    $rfqData = @{
        title = "Test RFQ"
        product = "Test Product"
        description = "Test Description"
        quantity = "100 units"
        location = "Stockholm"
        deliveryDate = "2025-08-10T00:00:00.000Z"
        budget = 15000
        notes = "Test notes"
        deadline = "2025-08-03T19:06:00.000Z"
    }
    
    $rfqResponse = Invoke-RestMethod -Uri "http://localhost:5001/rfqs" -Method POST -Headers @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $token"
    } -Body (ConvertTo-Json $rfqData) -TimeoutSec 10
    
    Write-Host "RFQ creation successful!" -ForegroundColor Green
    Write-Host "RFQ ID: $($rfqResponse.id)" -ForegroundColor Gray
    Write-Host "Title: $($rfqResponse.title)" -ForegroundColor Gray
    
} catch {
    Write-Host "RFQ creation failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Body: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error body" -ForegroundColor Red
        }
    }
}

Write-Host "`n4. Next Steps:" -ForegroundColor Cyan
Write-Host "If RFQ creation was successful, check the database for notifications:" -ForegroundColor White
Write-Host "SELECT * FROM Notifications WHERE Type='message' ORDER BY CreatedAt DESC;" -ForegroundColor Gray
Write-Host "If RFQ creation failed, check the error message above" -ForegroundColor White 