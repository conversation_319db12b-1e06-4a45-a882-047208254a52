Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "backend", "backend", "{1AE8ACA6-933B-BF2A-3671-3E2EAC007D16}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bulkit.API", "backend\Bulkit.API\Bulkit.API.csproj", "{C4B87123-CD9C-1DCC-CF58-865B485E6631}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Frontend (Next.js)", "Frontend (Next.js)", "{2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}"
	ProjectSection(SolutionItems) = preProject
		package.json = package.json
		next.config.js = next.config.js
		tailwind.config.ts = tailwind.config.ts
		tsconfig.json = tsconfig.json
		.eslintrc.json = .eslintrc.json
		postcss.config.js = postcss.config.js
		components.json = components.json
		next-env.d.ts = next-env.d.ts
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "app", "app", "{3CF0BEC8-B55D-4F4A-A695-5F4GAE009F38}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "components", "components", "{4DF1CED9-C66E-5F5B-B7A6-6F5HBF00AG49}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "lib", "lib", "{5EG2DFE0-D77F-6G6C-C8B7-7G6ICG01BH50}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "hooks", "hooks", "{6FH3EGF1-E88G-7H7D-D9C8-8H7JDH02CI61}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "types", "types", "{7GI4FHG2-F99H-8I8E-EAD9-9I8KEI03DJ72}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "public", "public", "{8HJ5GIH3-G00I-9J9F-FBEA-0J9LFJ04EK83}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C4B87123-CD9C-1DCC-CF58-865B485E6631} = {1AE8ACA6-933B-BF2A-3671-3E2EAC007D16}
		{3CF0BEC8-B55D-4F4A-A695-5F4GAE009F38} = {2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}
		{4DF1CED9-C66E-5F5B-B7A6-6F5HBF00AG49} = {2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}
		{5EG2DFE0-D77F-6G6C-C8B7-7G6ICG01BH50} = {2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}
		{6FH3EGF1-E88G-7H7D-D9C8-8H7JDH02CI61} = {2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}
		{7GI4FHG2-F99H-8I8E-EAD9-9I8KEI03DJ72} = {2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}
		{8HJ5GIH3-G00I-9J9F-FBEA-0J9LFJ04EK83} = {2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {93F3A1E2-C9E6-4C2D-B96E-5E40644206A0}
	EndGlobalSection
EndGlobal
