Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "backend", "backend", "{1AE8ACA6-933B-BF2A-3671-3E2EAC007D16}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bulkit.API", "backend\Bulkit.API\Bulkit.API.csproj", "{C4B87123-CD9C-1DCC-CF58-865B485E6631}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "frontend", "frontend", "{2BF9ADB7-A44C-4F3A-9584-4E3FAD008E27}"
	ProjectSection(SolutionItems) = preProject
		package.json = package.json
		next.config.js = next.config.js
		tailwind.config.ts = tailwind.config.ts
		tsconfig.json = tsconfig.json
		.eslintrc.json = .eslintrc.json
		postcss.config.js = postcss.config.js
		components.json = components.json
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4B87123-CD9C-1DCC-CF58-865B485E6631}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C4B87123-CD9C-1DCC-CF58-865B485E6631} = {1AE8ACA6-933B-BF2A-3671-3E2EAC007D16}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {93F3A1E2-C9E6-4C2D-B96E-5E40644206A0}
	EndGlobalSection
EndGlobal
