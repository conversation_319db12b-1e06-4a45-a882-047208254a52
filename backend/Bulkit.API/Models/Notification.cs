using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.Models;

public class Notification
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    
    // Push subscription fields (for web push notifications)
    public string? Endpoint { get; set; }
    public string? P256DH { get; set; }
    public string? Auth { get; set; }
    public string? UserAgent { get; set; }
    public bool IsActive { get; set; } = true;
    
    // Message fields (for storing notification content)
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public string? Badge { get; set; }
    public string? Url { get; set; }
    public string? Data { get; set; } // JSON data
    
    // Status fields
    public bool IsSent { get; set; } = false;
    public bool IsRead { get; set; } = false;
    public string Type { get; set; } = "general"; // new_rfq, bid_received, etc.
    
    // Timestamps
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? SentAt { get; set; }
    public DateTime? ReadAt { get; set; }

    // Navigation property
    public User User { get; set; } = null!;
} 