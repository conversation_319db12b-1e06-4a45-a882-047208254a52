using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bulkit.API.Models;

public class Rating
{
    public Guid Id { get; set; }
    
    [Required]
    [Foreign<PERSON><PERSON>(nameof(Bid))]
    public Guid BidId { get; set; }
    
    [Required]
    [Foreign<PERSON><PERSON>(nameof(Buyer))]
    public Guid BuyerId { get; set; }
    
    [Required]
    [ForeignKey(nameof(Vendor))]
    public Guid VendorId { get; set; }
    
    [Required]
    [Range(1, 5)]
    public int Stars { get; set; }
    
    [Required]
    [MaxLength(1000)]
    public string Comment { get; set; } = string.Empty;
    
    [Required]
    public DateTime CreatedAt { get; set; }
    
    // Navigation properties
    public virtual Bid Bid { get; set; } = null!;
    public virtual User Buyer { get; set; } = null!;
    public virtual User Vendor { get; set; } = null!;
} 