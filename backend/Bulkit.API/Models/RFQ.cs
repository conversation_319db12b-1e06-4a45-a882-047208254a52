using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bulkit.API.Models;

public class RFQ
{
    public Guid Id { get; set; }
    
    [Required]
    [ForeignKey(nameof(Buyer))]
    public Guid BuyerId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string Product { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(2000)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(200)]
    public string Quantity { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(500)]
    public string Location { get; set; } = string.Empty;
    
    [Required]
    public DateTime DeliveryDate { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal? Budget { get; set; }
    
    [MaxLength(2000)]
    public string? Notes { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Status { get; set; } = "active"; // 'active', 'pending', 'awarded', 'closed'
    
    [Required]
    public DateTime CreatedAt { get; set; }
    
    public DateTime UpdatedAt { get; set; }
    
    [Required]
    public DateTime Deadline { get; set; }
    
    // Navigation properties
    public virtual User Buyer { get; set; } = null!;
    public virtual ICollection<Bid> Bids { get; set; } = new List<Bid>();
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
} 