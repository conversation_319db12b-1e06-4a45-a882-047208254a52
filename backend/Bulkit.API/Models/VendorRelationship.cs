using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.Models;

public class VendorRelationship
{
    public Guid Id { get; set; }
    
    public Guid BuyerId { get; set; }
    public Guid VendorId { get; set; }
    
    public string Status { get; set; } = "pending"; // pending, approved, rejected, blocked
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public User Buyer { get; set; } = null!;
    public User Vendor { get; set; } = null!;
} 