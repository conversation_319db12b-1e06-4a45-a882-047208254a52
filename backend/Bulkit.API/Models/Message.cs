using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bulkit.API.Models;

public class Message
{
    public Guid Id { get; set; }
    
    [Required]
    [Foreign<PERSON><PERSON>(nameof(RFQ))]
    public Guid RfqId { get; set; }
    
    [Required]
    [<PERSON><PERSON><PERSON>(nameof(Sender))]
    public Guid SenderId { get; set; }
    
    [Required]
    [Foreign<PERSON><PERSON>(nameof(Receiver))]
    public Guid ReceiverId { get; set; }
    
    [Required]
    [MaxLength(2000)]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    public DateTime Timestamp { get; set; }
    
    public bool Read { get; set; } = false;
    
    // Navigation properties
    public virtual RFQ RFQ { get; set; } = null!;
    public virtual User Sender { get; set; } = null!;
    public virtual User Receiver { get; set; } = null!;
} 