using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bulkit.API.Models;

public class Bid
{
    public Guid Id { get; set; }
    
    [Required]
    [ForeignKey(nameof(RFQ))]
    public Guid RfqId { get; set; }
    
    [Required]
    [ForeignKey(nameof(Vendor))]
    public Guid VendorId { get; set; }
    
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Price { get; set; }
    
    [Required]
    [MaxLength(10)]
    public string Currency { get; set; } = "USD";
    
    [Required]
    [MaxLength(100)]
    public string DeliveryTime { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(2000)]
    public string Message { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Status { get; set; } = "pending"; // 'pending', 'awarded', 'rejected'
    
    [Required]
    public DateTime SubmittedAt { get; set; }
    
    public DateTime UpdatedAt { get; set; }
    
    // Navigation properties
    public virtual RFQ RFQ { get; set; } = null!;
    public virtual User Vendor { get; set; } = null!;
    public virtual ICollection<Rating> Ratings { get; set; } = new List<Rating>();
} 