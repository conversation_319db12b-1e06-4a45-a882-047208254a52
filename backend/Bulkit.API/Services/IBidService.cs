using Bulkit.API.DTOs;

namespace Bulkit.API.Services;

public interface IBidService
{
    Task<IEnumerable<BidDto>> GetBidsForRfqAsync(Guid rfqId);
    Task<BidDto?> GetBidByIdAsync(Guid bidId);
    Task<BidDto> CreateBidAsync(Guid rfqId, CreateBidDto createBidDto, Guid vendorId);
    Task<BidDto?> UpdateBidAsync(Guid bidId, UpdateBidDto updateBidDto, Guid vendorId);
    Task<bool> DeleteBidAsync(Guid bidId, Guid vendorId);
    Task<IEnumerable<BidDto>> GetBidsForVendorAsync(Guid vendorId);
    Task<IEnumerable<BidDto>> GetBidsForBuyerAsync(Guid buyerId);
    Task<BidDto?> UpdateBidStatusAsync(Guid bidId, string status, Guid buyerId);
} 