using Bulkit.API.DTOs;

namespace Bulkit.API.Services;

public interface IVendorRelationshipService
{
    Task<IEnumerable<VendorListDto>> GetVendorListAsync(Guid buyerId);
    Task<VendorRelationshipDto> CreateRelationshipAsync(Guid buyerId, CreateVendorRelationshipDto dto);
    Task<VendorRelationshipDto> UpdateRelationshipAsync(Guid buyerId, Guid vendorId, UpdateVendorRelationshipDto dto);
    Task<bool> DeleteRelationshipAsync(Guid buyerId, Guid vendorId);
    Task<IEnumerable<Guid>> GetApprovedVendorIdsAsync(Guid buyerId);
} 