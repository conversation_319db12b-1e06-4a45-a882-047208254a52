using Microsoft.EntityFrameworkCore;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;

namespace Bulkit.API.Services;

public class RfqService : IRfqService
{
    private readonly BulkitDbContext _context;
    private readonly INotificationService _notificationService;

    public RfqService(BulkitDbContext context, INotificationService notificationService)
    {
        _context = context;
        _notificationService = notificationService;
    }

    public async Task<IEnumerable<RfqSummaryDto>> GetRfqsForUserAsync(Guid userId, string userRole)
    {
        var query = _context.RFQs.AsQueryable();

        if (userRole == "buyer")
        {
            // Buyers see only their own RFQs
            query = query.Where(r => r.BuyerId == userId);
        }
        else if (userRole == "vendor")
        {
            // Vendors see all active RFQs that haven't expired
            query = query.Where(r => r.Status == "active" && r.Deadline > DateTime.UtcNow);
        }

        var rfqs = await query
            .Include(r => r.Bids)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync();

        return rfqs.Select(MapToRfqSummaryDto);
    }

    public async Task<RfqDto?> GetRfqByIdAsync(Guid rfqId, Guid? userId = null)
    {
        var rfq = await _context.RFQs
            .Include(r => r.Buyer)
            .Include(r => r.Bids)
                .ThenInclude(b => b.Vendor)
            .Include(r => r.Bids)
                .ThenInclude(b => b.Ratings)
                    .ThenInclude(rt => rt.Buyer)
            .FirstOrDefaultAsync(r => r.Id == rfqId);

        if (rfq == null) return null;

        return MapToRfqDto(rfq);
    }

    public async Task<RfqDto> CreateRfqAsync(CreateRfqDto createRfqDto, Guid buyerId)
    {
        var rfq = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyerId,
            Title = createRfqDto.Title,
            Product = createRfqDto.Product,
            Description = createRfqDto.Description,
            Quantity = createRfqDto.Quantity,
            Location = createRfqDto.Location,
            DeliveryDate = createRfqDto.DeliveryDate,
            Budget = createRfqDto.Budget,
            Notes = createRfqDto.Notes,
            Deadline = createRfqDto.Deadline,
            Status = "active",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.RFQs.Add(rfq);
        await _context.SaveChangesAsync();

        // Send notification to all vendors about the new RFQ
        try
        {
            Console.WriteLine($"Creating RFQ notification for product: {rfq.Product}");
            
            var notificationDto = new SendNotificationDto(
                "🔔 New RFQ Available!",
                $"A new RFQ for '{rfq.Product}' has been posted. Budget: ${rfq.Budget?.ToString() ?? "Not specified"}",
                null, // No icon to avoid 404 errors
                null, // No badge to avoid 404 errors
                $"/dashboard?tab=rfqs&highlight={rfq.Id}", // URL for direct navigation
                System.Text.Json.JsonSerializer.Serialize(new { 
                    rfqId = rfq.Id.ToString(), 
                    type = "new_rfq",
                    title = rfq.Title,
                    product = rfq.Product
                })
            );

            Console.WriteLine($"Notification DTO created: {notificationDto.Title} - {notificationDto.Body}");

            // Send notifications synchronously to ensure they're created
            Console.WriteLine($"Starting to send notification to vendors for buyer: {buyerId}");
            var result = await _notificationService.SendNotificationToVendorsAsync(notificationDto, buyerId);
            Console.WriteLine($"Notification sending completed. Success: {result}");
        }
        catch (Exception ex)
        {
            // Log error but don't fail RFQ creation
            Console.WriteLine($"Failed to prepare RFQ notification: {ex.Message}");
            Console.WriteLine($"Exception details: {ex}");
        }

        return await GetRfqByIdAsync(rfq.Id) ?? throw new InvalidOperationException("Failed to retrieve created RFQ");
    }

    public async Task<RfqDto?> UpdateRfqAsync(Guid rfqId, UpdateRfqDto updateRfqDto, Guid buyerId)
    {
        var rfq = await _context.RFQs
            .FirstOrDefaultAsync(r => r.Id == rfqId && r.BuyerId == buyerId);

        if (rfq == null) return null;

        // Update only provided fields
        if (!string.IsNullOrEmpty(updateRfqDto.Title))
            rfq.Title = updateRfqDto.Title;
        if (!string.IsNullOrEmpty(updateRfqDto.Product))
            rfq.Product = updateRfqDto.Product;
        if (!string.IsNullOrEmpty(updateRfqDto.Description))
            rfq.Description = updateRfqDto.Description;
        if (!string.IsNullOrEmpty(updateRfqDto.Quantity))
            rfq.Quantity = updateRfqDto.Quantity;
        if (!string.IsNullOrEmpty(updateRfqDto.Location))
            rfq.Location = updateRfqDto.Location;
        if (updateRfqDto.DeliveryDate.HasValue)
            rfq.DeliveryDate = updateRfqDto.DeliveryDate.Value;
        if (updateRfqDto.Budget.HasValue)
            rfq.Budget = updateRfqDto.Budget.Value;
        if (updateRfqDto.Notes != null)
            rfq.Notes = updateRfqDto.Notes;
        if (updateRfqDto.Deadline.HasValue)
            rfq.Deadline = updateRfqDto.Deadline.Value;
        if (!string.IsNullOrEmpty(updateRfqDto.Status))
            rfq.Status = updateRfqDto.Status;

        rfq.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return await GetRfqByIdAsync(rfq.Id);
    }

    public async Task<bool> DeleteRfqAsync(Guid rfqId, Guid buyerId)
    {
        var rfq = await _context.RFQs
            .FirstOrDefaultAsync(r => r.Id == rfqId && r.BuyerId == buyerId);

        if (rfq == null) return false;

        _context.RFQs.Remove(rfq);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<RfqDto?> AwardBidAsync(Guid rfqId, Guid bidId, Guid buyerId)
    {
        var rfq = await _context.RFQs
            .Include(r => r.Bids)
            .FirstOrDefaultAsync(r => r.Id == rfqId && r.BuyerId == buyerId);

        if (rfq == null) return null;

        var winningBid = rfq.Bids.FirstOrDefault(b => b.Id == bidId);
        if (winningBid == null) return null;

        // Update RFQ status
        rfq.Status = "awarded";
        rfq.UpdatedAt = DateTime.UtcNow;

        // Update bid statuses
        foreach (var bid in rfq.Bids)
        {
            bid.Status = bid.Id == bidId ? "awarded" : "rejected";
            bid.UpdatedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();

        return await GetRfqByIdAsync(rfq.Id);
    }

    public async Task<bool> CanEditRfqAsync(Guid rfqId, Guid buyerId)
    {
        var rfq = await _context.RFQs
            .Include(r => r.Bids)
            .FirstOrDefaultAsync(r => r.Id == rfqId && r.BuyerId == buyerId);

        if (rfq == null) return false;

        // Can edit if RFQ is active and no bids have been awarded
        return rfq.Status == "active" && !rfq.Bids.Any(b => b.Status == "awarded");
    }

    public async Task<bool> CanDeleteRfqAsync(Guid rfqId, Guid buyerId)
    {
        var rfq = await _context.RFQs
            .Include(r => r.Bids)
            .FirstOrDefaultAsync(r => r.Id == rfqId && r.BuyerId == buyerId);

        if (rfq == null) return false;

        // Can delete if RFQ is active and no bids have been awarded
        return rfq.Status == "active" && !rfq.Bids.Any(b => b.Status == "awarded");
    }

    private static RfqSummaryDto MapToRfqSummaryDto(RFQ rfq)
    {
        return new RfqSummaryDto(
            rfq.Id.ToString(),
            rfq.BuyerId.ToString(),
            rfq.Title,
            rfq.Product,
            rfq.Quantity,
            rfq.Location,
            rfq.DeliveryDate,
            rfq.Budget,
            rfq.Status,
            rfq.CreatedAt,
            rfq.Deadline,
            rfq.Bids.Count
        );
    }

    private static RfqDto MapToRfqDto(RFQ rfq)
    {
        return new RfqDto(
            rfq.Id.ToString(),
            rfq.BuyerId.ToString(),
            rfq.Title,
            rfq.Product,
            rfq.Description,
            rfq.Quantity,
            rfq.Location,
            rfq.DeliveryDate,
            rfq.Budget,
            rfq.Notes,
            rfq.Status,
            rfq.CreatedAt,
            rfq.UpdatedAt,
            rfq.Deadline,
            MapToUserDto(rfq.Buyer),
            rfq.Bids.Select(MapToBidDto).ToList(),
            rfq.Bids.Count
        );
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );
    }

    private static BidDto MapToBidDto(Bid bid)
    {
        return new BidDto(
            bid.Id.ToString(),
            bid.RfqId.ToString(),
            bid.VendorId.ToString(),
            bid.Price,
            bid.Currency,
            bid.DeliveryTime,
            bid.Message,
            bid.Status,
            bid.SubmittedAt,
            bid.UpdatedAt,
            MapToUserDto(bid.Vendor),
            bid.Ratings.Select(MapToRatingDto).ToList()
        );
    }

    private static RatingDto MapToRatingDto(Rating rating)
    {
        return new RatingDto(
            rating.Id.ToString(),
            rating.BidId.ToString(),
            rating.BuyerId.ToString(),
            rating.VendorId.ToString(),
            rating.Stars,
            rating.Comment,
            rating.CreatedAt,
            MapToUserDto(rating.Buyer)
        );
    }
} 