using Microsoft.EntityFrameworkCore;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;

namespace Bulkit.API.Services;

public class RatingService : IRatingService
{
    private readonly BulkitDbContext _context;

    public RatingService(BulkitDbContext context)
    {
        _context = context;
    }

    public async Task<RatingDto> CreateRatingAsync(Guid bidId, CreateRatingDto createRatingDto, Guid buyerId)
    {
        // Verify bid exists and is awarded
        var bid = await _context.Bids
            .Include(b => b.RFQ)
            .Include(b => b.Vendor)
            .FirstOrDefaultAsync(b => b.Id == bidId);

        if (bid == null)
        {
            throw new InvalidOperationException("Bid not found");
        }

        if (bid.Status != "awarded")
        {
            throw new InvalidOperationException("Can only rate awarded bids");
        }

        if (bid.RFQ.BuyerId != buyerId)
        {
            throw new UnauthorizedAccessException("Only the buyer who created the RFQ can rate bids");
        }

        // Check if rating already exists
        var existingRating = await _context.Ratings
            .FirstOrDefaultAsync(r => r.BidId == bidId && r.BuyerId == buyerId);

        if (existingRating != null)
        {
            throw new InvalidOperationException("You have already rated this bid");
        }

        var rating = new Rating
        {
            Id = Guid.NewGuid(),
            BidId = bidId,
            BuyerId = buyerId,
            VendorId = bid.VendorId,
            Stars = createRatingDto.Stars,
            Comment = createRatingDto.Comment,
            CreatedAt = DateTime.UtcNow
        };

        _context.Ratings.Add(rating);
        await _context.SaveChangesAsync();

        // Reload rating with navigation properties
        var savedRating = await _context.Ratings
            .Include(r => r.Buyer)
            .FirstAsync(r => r.Id == rating.Id);

        return MapToRatingDto(savedRating);
    }

    public async Task<IEnumerable<RatingDto>> GetRatingsForVendorAsync(Guid vendorId)
    {
        var ratings = await _context.Ratings
            .Include(r => r.Buyer)
            .Where(r => r.VendorId == vendorId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync();

        return ratings.Select(MapToRatingDto);
    }

    public async Task<RatingDto?> GetRatingForBidAsync(Guid bidId, Guid buyerId)
    {
        var rating = await _context.Ratings
            .Include(r => r.Buyer)
            .FirstOrDefaultAsync(r => r.BidId == bidId && r.BuyerId == buyerId);

        return rating == null ? null : MapToRatingDto(rating);
    }

    public async Task<double> GetAverageRatingForVendorAsync(Guid vendorId)
    {
        var ratings = await _context.Ratings
            .Where(r => r.VendorId == vendorId)
            .Select(r => r.Stars)
            .ToListAsync();

        return ratings.Any() ? ratings.Average() : 0.0;
    }

    private static RatingDto MapToRatingDto(Rating rating)
    {
        return new RatingDto(
            rating.Id.ToString(),
            rating.BidId.ToString(),
            rating.BuyerId.ToString(),
            rating.VendorId.ToString(),
            rating.Stars,
            rating.Comment,
            rating.CreatedAt,
            MapToUserDto(rating.Buyer)
        );
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );
    }
} 