using Microsoft.EntityFrameworkCore;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;

namespace Bulkit.API.Services;

public class MessageService : IMessageService
{
    private readonly BulkitDbContext _context;

    public MessageService(BulkitDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<MessageDto>> GetMessagesForRfqAsync(Guid rfqId, Guid userId)
    {
        // Verify user has access to this RFQ (either buyer or has bid on it)
        var rfq = await _context.RFQs
            .Include(r => r.Bids)
            .FirstOrDefaultAsync(r => r.Id == rfqId);

        if (rfq == null)
        {
            throw new InvalidOperationException("RFQ not found");
        }

        var hasAccess = rfq.BuyerId == userId || rfq.Bids.Any(b => b.VendorId == userId);
        if (!hasAccess)
        {
            throw new UnauthorizedAccessException("You don't have access to messages for this RFQ");
        }

        var messages = await _context.Messages
            .Include(m => m.Sender)
            .Include(m => m.Receiver)
            .Where(m => m.RfqId == rfqId)
            .OrderBy(m => m.Timestamp)
            .ToListAsync();

        return messages.Select(MapToMessageDto);
    }

    public async Task<MessageDto> SendMessageAsync(Guid rfqId, SendMessageDto sendMessageDto, Guid senderId)
    {
        var receiverId = Guid.Parse(sendMessageDto.ReceiverId);

        // Verify RFQ exists
        var rfq = await _context.RFQs
            .Include(r => r.Bids)
            .FirstOrDefaultAsync(r => r.Id == rfqId);

        if (rfq == null)
        {
            throw new InvalidOperationException("RFQ not found");
        }

        // Verify sender has access to this RFQ
        var senderHasAccess = rfq.BuyerId == senderId || rfq.Bids.Any(b => b.VendorId == senderId);
        if (!senderHasAccess)
        {
            throw new UnauthorizedAccessException("You don't have access to send messages for this RFQ");
        }

        // Verify receiver has access to this RFQ
        var receiverHasAccess = rfq.BuyerId == receiverId || rfq.Bids.Any(b => b.VendorId == receiverId);
        if (!receiverHasAccess)
        {
            throw new InvalidOperationException("Receiver doesn't have access to this RFQ");
        }

        // Verify receiver exists
        var receiver = await _context.Users.FindAsync(receiverId);
        if (receiver == null)
        {
            throw new InvalidOperationException("Receiver not found");
        }

        var message = new Message
        {
            Id = Guid.NewGuid(),
            RfqId = rfqId,
            SenderId = senderId,
            ReceiverId = receiverId,
            Content = sendMessageDto.Content,
            Timestamp = DateTime.UtcNow,
            Read = false
        };

        _context.Messages.Add(message);
        await _context.SaveChangesAsync();

        // Reload message with navigation properties
        var savedMessage = await _context.Messages
            .Include(m => m.Sender)
            .Include(m => m.Receiver)
            .FirstAsync(m => m.Id == message.Id);

        return MapToMessageDto(savedMessage);
    }

    public async Task<bool> MarkAsReadAsync(Guid messageId, Guid userId)
    {
        var message = await _context.Messages
            .FirstOrDefaultAsync(m => m.Id == messageId && m.ReceiverId == userId);

        if (message == null) return false;

        message.Read = true;
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<int> GetUnreadCountAsync(Guid userId)
    {
        return await _context.Messages
            .CountAsync(m => m.ReceiverId == userId && !m.Read);
    }

    private static MessageDto MapToMessageDto(Message message)
    {
        return new MessageDto(
            message.Id.ToString(),
            message.RfqId.ToString(),
            message.SenderId.ToString(),
            message.ReceiverId.ToString(),
            message.Content,
            message.Timestamp,
            message.Read,
            MapToUserDto(message.Sender),
            MapToUserDto(message.Receiver)
        );
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );
    }
} 