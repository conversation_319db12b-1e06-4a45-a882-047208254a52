using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using BCrypt.Net;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;

namespace Bulkit.API.Services;

public class AuthService : IAuthService
{
    private readonly BulkitDbContext _context;
    private readonly IConfiguration _configuration;

    public AuthService(BulkitDbContext context, IConfiguration configuration)
    {
        _context = context;
        _configuration = configuration;
    }

    public async Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto)
    {
        // Check if user already exists
        var existingUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == registerDto.Email);

        if (existingUser != null)
        {
            throw new InvalidOperationException("User with this email already exists");
        }

        // Validate role
        if (registerDto.Role.ToLower() != "buyer" && registerDto.Role.ToLower() != "vendor")
        {
            throw new InvalidOperationException("Role must be either 'buyer' or 'vendor'");
        }

        // Create new user
        var user = new User
        {
            Id = Guid.NewGuid(),
            Email = registerDto.Email.ToLower(),
            Role = registerDto.Role.ToLower(),
            CompanyName = registerDto.CompanyName,
            ContactName = registerDto.ContactName,
            Phone = registerDto.Phone,
            Address = registerDto.Address,
            City = registerDto.City,
            Country = registerDto.Country,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(registerDto.Password),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        var token = GenerateJwtToken(user);
        var userDto = MapToUserDto(user);

        return new AuthResponseDto(token, userDto);
    }

    public async Task<AuthResponseDto> LoginAsync(LoginDto loginDto)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == loginDto.Email.ToLower());

        if (user == null || !BCrypt.Net.BCrypt.Verify(loginDto.Password, user.PasswordHash))
        {
            throw new UnauthorizedAccessException("Invalid email or password");
        }

        var token = GenerateJwtToken(user);
        var userDto = MapToUserDto(user);

        return new AuthResponseDto(token, userDto);
    }

    public async Task<User?> GetUserByIdAsync(Guid userId)
    {
        return await _context.Users.FindAsync(userId);
    }

    public string GenerateJwtToken(User user)
    {
        var jwtSettings = _configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"];
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim(ClaimTypes.Role, user.Role),
            new Claim("companyName", user.CompanyName),
            new Claim("contactName", user.ContactName)
        };

        var token = new JwtSecurityToken(
            issuer: jwtSettings["Issuer"],
            audience: jwtSettings["Audience"],
            claims: claims,
            expires: DateTime.UtcNow.AddDays(7),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );
    }
} 