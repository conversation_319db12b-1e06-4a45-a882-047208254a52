using Bulkit.API.DTOs;

namespace Bulkit.API.Services;

public interface IRfqService
{
    Task<IEnumerable<RfqSummaryDto>> GetRfqsForUserAsync(Guid userId, string userRole);
    Task<RfqDto?> GetRfqByIdAsync(Guid rfqId, Guid? userId = null);
    Task<RfqDto> CreateRfqAsync(CreateRfqDto createRfqDto, Guid buyerId);
    Task<RfqDto?> UpdateRfqAsync(Guid rfqId, UpdateRfqDto updateRfqDto, Guid buyerId);
    Task<bool> DeleteRfqAsync(Guid rfqId, Guid buyerId);
    Task<RfqDto?> AwardBidAsync(Guid rfqId, Guid bidId, Guid buyerId);
    Task<bool> CanEditRfqAsync(Guid rfqId, Guid buyerId);
    Task<bool> CanDeleteRfqAsync(Guid rfqId, Guid buyerId);
} 