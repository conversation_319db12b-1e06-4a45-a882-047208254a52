using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using WebPush;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;

namespace Bulkit.API.Services;

public class NotificationService : INotificationService
{
    private readonly BulkitDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly WebPushClient _webPushClient;
    private readonly VapidDetails _vapidDetails;

    public NotificationService(BulkitDbContext context, IConfiguration configuration)
    {
        _context = context;
        _configuration = configuration;
        _webPushClient = new WebPushClient();
        
        try
        {
            // Generate new VAPID keys
            var vapidKeys = VapidHelper.GenerateVapidKeys();
            var subject = _configuration["Notifications:VapidSubject"] ?? "mailto:<EMAIL>";

            // Create VapidDetails for sending notifications
            _vapidDetails = new VapidDetails(subject, vapidKeys.PublicKey, vapidKeys.PrivateKey);
            
            Console.WriteLine($"NotificationService initialized with VAPID keys:");
            Console.WriteLine($"Public Key: {vapidKeys.PublicKey}");
            Console.WriteLine($"Subject: {subject}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing NotificationService: {ex.Message}");
            throw; // Re-throw to prevent service from starting with invalid keys
        }
    }

    public async Task<NotificationSubscriptionDto> CreateSubscriptionAsync(Guid userId, CreateNotificationSubscriptionDto subscriptionDto)
    {
        // Check if subscription already exists
        var existingSubscription = await _context.Notifications
            .FirstOrDefaultAsync(n => n.UserId == userId && n.Endpoint == subscriptionDto.Endpoint);

        if (existingSubscription != null)
        {
            // Update existing subscription
            existingSubscription.P256DH = subscriptionDto.P256DH;
            existingSubscription.Auth = subscriptionDto.Auth;
            existingSubscription.UserAgent = subscriptionDto.UserAgent ?? string.Empty;
            existingSubscription.IsActive = true;
            existingSubscription.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return MapToSubscriptionDto(existingSubscription);
        }

        // Create new subscription
        var subscription = new Notification
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Endpoint = subscriptionDto.Endpoint,
            P256DH = subscriptionDto.P256DH,
            Auth = subscriptionDto.Auth,
            UserAgent = subscriptionDto.UserAgent ?? string.Empty,
            IsActive = true,
            Title = "", // Empty for subscription records
            Body = "", // Empty for subscription records
            Type = "subscription",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Notifications.Add(subscription);
        await _context.SaveChangesAsync();

        return MapToSubscriptionDto(subscription);
    }

    public async Task<bool> DeleteSubscriptionAsync(Guid userId, string endpoint)
    {
        var subscription = await _context.Notifications
            .FirstOrDefaultAsync(n => n.UserId == userId && n.Endpoint == endpoint);

        if (subscription == null)
            return false;

        _context.Notifications.Remove(subscription);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<NotificationSubscriptionDto>> GetUserSubscriptionsAsync(Guid userId)
    {
        var subscriptions = await _context.Notifications
            .Where(n => n.UserId == userId && n.IsActive)
            .ToListAsync();

        return subscriptions.Select(MapToSubscriptionDto);
    }

    public async Task<VapidKeysDto> GetVapidKeysAsync()
    {
        // Return the public key in URL-safe base64 format
        return new VapidKeysDto(_vapidDetails.PublicKey, _vapidDetails.PrivateKey);
    }

    public async Task<NotificationMessageDto> SendNotificationAsync(Guid userId, SendNotificationDto notificationDto)
    {
        Console.WriteLine($"SendNotificationAsync called for user {userId}");

        // Create notification record
        var notification = new Notification
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            Title = notificationDto.Title,
            Body = notificationDto.Body,
            Icon = notificationDto.Icon,
            Badge = notificationDto.Badge,
            Url = notificationDto.Url,
            Data = notificationDto.Data,
            Type = "message",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Notifications.Add(notification);
        await _context.SaveChangesAsync();
        Console.WriteLine($"Created notification record: {notification.Id}");

        // Get user's active subscriptions
        var subscriptions = await _context.Notifications
            .Where(n => n.UserId == userId && n.IsActive && n.Type == "subscription" && !string.IsNullOrEmpty(n.Endpoint))
            .ToListAsync();

        Console.WriteLine($"Found {subscriptions.Count} active subscriptions for user {userId}");

        if (subscriptions.Count == 0)
        {
            Console.WriteLine($"No active subscriptions found for user {userId}. Notification will be saved but not sent.");
            notification.IsSent = false;
            await _context.SaveChangesAsync();
            return MapToNotificationMessageDto(notification);
        }

        // Send push notifications to all subscriptions
        var sendTasks = subscriptions.Select(async subscription =>
        {
            try
            {
                var pushSubscription = new PushSubscription(
                    subscription.Endpoint,
                    subscription.P256DH,
                    subscription.Auth
                );

                var payload = JsonSerializer.Serialize(new
                {
                    title = notificationDto.Title,
                    body = notificationDto.Body,
                    icon = notificationDto.Icon,
                    badge = notificationDto.Badge,
                    url = notificationDto.Url,
                    data = notificationDto.Data,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                });

                await _webPushClient.SendNotificationAsync(
                    pushSubscription,
                    payload,
                    _vapidDetails
                );
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send notification to {subscription.Endpoint}: {ex.Message}");
                if (ex.Message.Contains("410") || ex.Message.Contains("expired"))
                {
                    subscription.IsActive = false;
                }
                return false;
            }
        });

        var results = await Task.WhenAll(sendTasks);
        var successCount = results.Count(r => r);

        notification.IsSent = successCount > 0;
        notification.SentAt = successCount > 0 ? DateTime.UtcNow : null;

        await _context.SaveChangesAsync();

        return MapToNotificationMessageDto(notification);
    }

    public async Task<bool> SendNotificationToVendorsAsync(SendNotificationDto notificationDto, Guid buyerId)
    {
        // For testing purposes, send to ALL vendors instead of just approved ones
        // TODO: Change back to approved vendors only in production
        var allVendorIds = await _context.Users
            .Where(u => u.Role == "vendor")
            .Select(u => u.Id)
            .ToListAsync();

        if (!allVendorIds.Any())
        {
            Console.WriteLine($"No vendors found in the system");
            return false;
        }

        // Get vendor users
        var vendorUsers = await _context.Users
            .Where(u => allVendorIds.Contains(u.Id))
            .ToListAsync();

        Console.WriteLine($"Sending notification to {vendorUsers.Count} ALL vendors for buyer {buyerId}: {notificationDto.Title}");

        var sendTasks = vendorUsers.Select(async vendor =>
        {
            try
            {
                Console.WriteLine($"Sending notification to vendor: {vendor.Email} ({vendor.Id})");
                await SendNotificationAsync(vendor.Id, notificationDto);
                Console.WriteLine($"Successfully sent notification to vendor: {vendor.Email}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send notification to vendor {vendor.Email}: {ex.Message}");
                return false;
            }
        });

        var results = await Task.WhenAll(sendTasks);
        var successCount = results.Count(r => r);
        Console.WriteLine($"Notification sending completed. Success: {successCount}/{vendorUsers.Count}");
        return results.Any(r => r);
    }

    public async Task<IEnumerable<NotificationMessageDto>> GetUserNotificationsAsync(Guid userId)
    {
        var notifications = await _context.Notifications
            .Where(n => n.UserId == userId && n.Type == "message")
            .OrderByDescending(n => n.CreatedAt)
            .Take(50) // Limit to recent notifications
            .ToListAsync();

        return notifications.Select(MapToNotificationMessageDto);
    }

    public async Task<bool> MarkNotificationAsReadAsync(Guid notificationId, Guid userId)
    {
        var notification = await _context.Notifications
            .FirstOrDefaultAsync(n => n.Id == notificationId && n.UserId == userId && n.Type == "message");

        if (notification == null)
            return false;

        notification.IsRead = true;
        notification.ReadAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        return true;
    }

    private static NotificationSubscriptionDto MapToSubscriptionDto(Notification notification)
    {
        return new NotificationSubscriptionDto(
            notification.Id.ToString(),
            notification.UserId.ToString(),
            notification.Endpoint ?? "",
            notification.IsActive,
            notification.CreatedAt
        );
    }

    private static NotificationMessageDto MapToNotificationMessageDto(Notification notification)
    {
        return new NotificationMessageDto(
            notification.Id.ToString(),
            notification.UserId.ToString(),
            notification.Title,
            notification.Body,
            notification.Icon,
            notification.Badge,
            notification.Url,
            notification.Data,
            notification.IsSent,
            notification.IsRead,
            notification.CreatedAt,
            notification.SentAt,
            notification.ReadAt
        );
    }
} 