using Bulkit.API.DTOs;

namespace Bulkit.API.Services;

public interface INotificationService
{
    Task<NotificationSubscriptionDto> CreateSubscriptionAsync(Guid userId, CreateNotificationSubscriptionDto subscriptionDto);
    Task<bool> DeleteSubscriptionAsync(Guid userId, string endpoint);
    Task<IEnumerable<NotificationSubscriptionDto>> GetUserSubscriptionsAsync(Guid userId);
    Task<VapidKeysDto> GetVapidKeysAsync();
    Task<NotificationMessageDto> SendNotificationAsync(Guid userId, SendNotificationDto notificationDto);
    Task<bool> SendNotificationToVendorsAsync(SendNotificationDto notificationDto, Guid buyerId);
    Task<IEnumerable<NotificationMessageDto>> GetUserNotificationsAsync(Guid userId);
    Task<bool> MarkNotificationAsReadAsync(Guid notificationId, Guid userId);
} 