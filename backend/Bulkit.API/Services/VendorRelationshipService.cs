using Microsoft.EntityFrameworkCore;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;

namespace Bulkit.API.Services;

public class VendorRelationshipService : IVendorRelationshipService
{
    private readonly BulkitDbContext _context;

    public VendorRelationshipService(BulkitDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<VendorListDto>> GetVendorListAsync(Guid buyerId)
    {
        // Get all vendors with their relationship status to this buyer
        var vendors = await _context.Users
            .Where(u => u.Role == "vendor")
            .Select(vendor => new
            {
                Vendor = vendor,
                Relationship = _context.VendorRelationships
                    .FirstOrDefault(r => r.BuyerId == buyerId && r.VendorId == vendor.Id)
            })
            .ToListAsync();

        return vendors.Select(v => new VendorListDto(
            v.Vendor.Id.ToString(),
            v.Vendor.CompanyName,
            v.Vendor.ContactName,
            v.Vendor.Email,
            v.Vendor.Phone ?? "",
            v.Vendor.City,
            v.Vendor.Country,
            v.Relationship?.Status ?? "none",
            v.Relationship?.CreatedAt ?? DateTime.UtcNow
        ));
    }

    public async Task<VendorRelationshipDto> CreateRelationshipAsync(Guid buyerId, CreateVendorRelationshipDto dto)
    {
        var vendorId = Guid.Parse(dto.VendorId);

        // Check if relationship already exists
        var existingRelationship = await _context.VendorRelationships
            .FirstOrDefaultAsync(r => r.BuyerId == buyerId && r.VendorId == vendorId);

        if (existingRelationship != null)
        {
            throw new InvalidOperationException("Relationship already exists");
        }

        // Verify vendor exists and is actually a vendor
        var vendor = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == vendorId && u.Role == "vendor");

        if (vendor == null)
        {
            throw new InvalidOperationException("Vendor not found");
        }

        var relationship = new VendorRelationship
        {
            Id = Guid.NewGuid(),
            BuyerId = buyerId,
            VendorId = vendorId,
            Status = "pending",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.VendorRelationships.Add(relationship);
        await _context.SaveChangesAsync();

        // Load the buyer and vendor for the DTO
        var buyer = await _context.Users.FindAsync(buyerId);

        return new VendorRelationshipDto(
            relationship.Id.ToString(),
            relationship.BuyerId.ToString(),
            relationship.VendorId.ToString(),
            relationship.Status,
            relationship.CreatedAt,
            relationship.UpdatedAt,
            MapToUserDto(buyer!),
            MapToUserDto(vendor)
        );
    }

    public async Task<VendorRelationshipDto> UpdateRelationshipAsync(Guid buyerId, Guid vendorId, UpdateVendorRelationshipDto dto)
    {
        var relationship = await _context.VendorRelationships
            .FirstOrDefaultAsync(r => r.BuyerId == buyerId && r.VendorId == vendorId);

        if (relationship == null)
        {
            throw new InvalidOperationException("Relationship not found");
        }

        relationship.Status = dto.Status;
        relationship.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        // Load the buyer and vendor for the DTO
        var buyer = await _context.Users.FindAsync(buyerId);
        var vendor = await _context.Users.FindAsync(vendorId);

        return new VendorRelationshipDto(
            relationship.Id.ToString(),
            relationship.BuyerId.ToString(),
            relationship.VendorId.ToString(),
            relationship.Status,
            relationship.CreatedAt,
            relationship.UpdatedAt,
            MapToUserDto(buyer!),
            MapToUserDto(vendor!)
        );
    }

    public async Task<bool> DeleteRelationshipAsync(Guid buyerId, Guid vendorId)
    {
        var relationship = await _context.VendorRelationships
            .FirstOrDefaultAsync(r => r.BuyerId == buyerId && r.VendorId == vendorId);

        if (relationship == null)
            return false;

        _context.VendorRelationships.Remove(relationship);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<Guid>> GetApprovedVendorIdsAsync(Guid buyerId)
    {
        return await _context.VendorRelationships
            .Where(r => r.BuyerId == buyerId && r.Status == "approved")
            .Select(r => r.VendorId)
            .ToListAsync();
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );
    }
} 