using Microsoft.EntityFrameworkCore;
using Bulkit.API.Data;
using Bulkit.API.DTOs;
using Bulkit.API.Models;
using Bulkit.API.Services;

namespace Bulkit.API.Services;

public class BidService : IBidService
{
    private readonly BulkitDbContext _context;
    private readonly INotificationService _notificationService;

    public BidService(BulkitDbContext context, INotificationService notificationService)
    {
        _context = context;
        _notificationService = notificationService;
    }

    public async Task<IEnumerable<BidDto>> GetBidsForRfqAsync(Guid rfqId)
    {
        var bids = await _context.Bids
            .Include(b => b.Vendor)
            .Include(b => b.Ratings)
                .ThenInclude(r => r.Buyer)
            .Where(b => b.RfqId == rfqId)
            .OrderByDescending(b => b.SubmittedAt)
            .ToListAsync();

        return bids.Select(MapToBidDto);
    }

    public async Task<BidDto?> GetBidByIdAsync(Guid bidId)
    {
        var bid = await _context.Bids
            .Include(b => b.Vendor)
            .Include(b => b.Ratings)
                .ThenInclude(r => r.Buyer)
            .FirstOrDefaultAsync(b => b.Id == bidId);

        return bid == null ? null : MapToBidDto(bid);
    }

    public async Task<BidDto> CreateBidAsync(Guid rfqId, CreateBidDto createBidDto, Guid vendorId)
    {
        // Check if RFQ exists and is active
        var rfq = await _context.RFQs.FindAsync(rfqId);
        if (rfq == null)
        {
            throw new InvalidOperationException("RFQ not found");
        }

        if (rfq.Status != "active")
        {
            throw new InvalidOperationException("Cannot bid on inactive RFQ");
        }

        if (rfq.Deadline <= DateTime.UtcNow)
        {
            throw new InvalidOperationException("RFQ deadline has passed");
        }

        // Check if vendor already has a bid for this RFQ
        var existingBid = await _context.Bids
            .FirstOrDefaultAsync(b => b.RfqId == rfqId && b.VendorId == vendorId);

        if (existingBid != null)
        {
            throw new InvalidOperationException("You have already submitted a bid for this RFQ");
        }

        var bid = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfqId,
            VendorId = vendorId,
            Price = createBidDto.Price,
            Currency = createBidDto.Currency,
            DeliveryTime = createBidDto.DeliveryTime,
            Message = createBidDto.Message,
            Status = "pending",
            SubmittedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Bids.Add(bid);
        await _context.SaveChangesAsync();

        // Load vendor information for notification
        var vendor = await _context.Users.FindAsync(vendorId);

        // Send notification to buyer about the new bid
        try
        {
            var notificationDto = new SendNotificationDto(
                "💰 New Bid Received!",
                $"A new bid has been submitted for '{rfq.Title}' by {vendor?.CompanyName ?? "a vendor"}",
                null, // No icon to avoid 404 errors
                null, // No badge to avoid 404 errors
                $"/dashboard?tab=bids&highlight={bid.Id}&rfq={rfqId}", // URL for direct navigation
                System.Text.Json.JsonSerializer.Serialize(new { 
                    rfqId = rfqId.ToString(), 
                    bidId = bid.Id.ToString(), 
                    type = "new_bid",
                    price = bid.Price,
                    currency = bid.Currency,
                    vendorName = vendor?.CompanyName
                })
            );

            Console.WriteLine($"Sending bid notification to buyer {rfq.BuyerId} from vendor {vendor?.CompanyName}");
            await _notificationService.SendNotificationAsync(rfq.BuyerId, notificationDto);
            Console.WriteLine("Bid notification sent successfully");
        }
        catch (Exception ex)
        {
            // Log error but don't fail bid creation
            Console.WriteLine($"Failed to send bid notification: {ex.Message}");
            Console.WriteLine($"Exception details: {ex}");
        }

        return await GetBidByIdAsync(bid.Id) ?? throw new InvalidOperationException("Failed to retrieve created bid");
    }

    public async Task<BidDto?> UpdateBidAsync(Guid bidId, UpdateBidDto updateBidDto, Guid vendorId)
    {
        var bid = await _context.Bids
            .Include(b => b.RFQ)
            .FirstOrDefaultAsync(b => b.Id == bidId && b.VendorId == vendorId);

        if (bid == null) return null;

        // Check if bid can be updated (must be pending and RFQ must be active)
        if (bid.Status != "pending")
        {
            throw new InvalidOperationException("Cannot update a bid that has been awarded or rejected");
        }

        if (bid.RFQ.Status != "active")
        {
            throw new InvalidOperationException("Cannot update bid on inactive RFQ");
        }

        // Update only provided fields
        if (updateBidDto.Price.HasValue)
            bid.Price = updateBidDto.Price.Value;
        if (!string.IsNullOrEmpty(updateBidDto.Currency))
            bid.Currency = updateBidDto.Currency;
        if (!string.IsNullOrEmpty(updateBidDto.DeliveryTime))
            bid.DeliveryTime = updateBidDto.DeliveryTime;
        if (!string.IsNullOrEmpty(updateBidDto.Message))
            bid.Message = updateBidDto.Message;

        bid.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return await GetBidByIdAsync(bid.Id);
    }

    public async Task<bool> DeleteBidAsync(Guid bidId, Guid vendorId)
    {
        var bid = await _context.Bids
            .Include(b => b.RFQ)
            .FirstOrDefaultAsync(b => b.Id == bidId && b.VendorId == vendorId);

        if (bid == null) return false;

        // Check if bid can be deleted (must be pending)
        if (bid.Status != "pending")
        {
            throw new InvalidOperationException("Cannot delete a bid that has been awarded or rejected");
        }

        _context.Bids.Remove(bid);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<IEnumerable<BidDto>> GetBidsForVendorAsync(Guid vendorId)
    {
        var bids = await _context.Bids
            .Include(b => b.Vendor)
            .Include(b => b.RFQ)
            .Include(b => b.Ratings)
                .ThenInclude(r => r.Buyer)
            .Where(b => b.VendorId == vendorId)
            .OrderByDescending(b => b.SubmittedAt)
            .ToListAsync();

        return bids.Select(MapToBidDto);
    }

    public async Task<IEnumerable<BidDto>> GetBidsForBuyerAsync(Guid buyerId)
    {
        var bids = await _context.Bids
            .Include(b => b.Vendor)
            .Include(b => b.RFQ)
            .Include(b => b.Ratings)
                .ThenInclude(r => r.Buyer)
            .Where(b => b.RFQ.BuyerId == buyerId)
            .OrderByDescending(b => b.SubmittedAt)
            .ToListAsync();

        return bids.Select(MapToBidDto);
    }

    private static BidDto MapToBidDto(Bid bid)
    {
        return new BidDto(
            bid.Id.ToString(),
            bid.RfqId.ToString(),
            bid.VendorId.ToString(),
            bid.Price,
            bid.Currency,
            bid.DeliveryTime,
            bid.Message,
            bid.Status,
            bid.SubmittedAt,
            bid.UpdatedAt,
            MapToUserDto(bid.Vendor),
            bid.Ratings.Select(MapToRatingDto).ToList()
        );
    }

    private static UserDto MapToUserDto(User user)
    {
        return new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );
    }

    private static RatingDto MapToRatingDto(Rating rating)
    {
        return new RatingDto(
            rating.Id.ToString(),
            rating.BidId.ToString(),
            rating.BuyerId.ToString(),
            rating.VendorId.ToString(),
            rating.Stars,
            rating.Comment,
            rating.CreatedAt,
            MapToUserDto(rating.Buyer)
        );
    }

    public async Task<BidDto?> UpdateBidStatusAsync(Guid bidId, string status, Guid buyerId)
    {
        var bid = await _context.Bids
            .Include(b => b.RFQ)
            .Include(b => b.Vendor)
            .Include(b => b.Ratings)
                .ThenInclude(r => r.Buyer)
            .FirstOrDefaultAsync(b => b.Id == bidId);

        if (bid == null || bid.RFQ.BuyerId != buyerId)
        {
            return null; // Bid not found or user doesn't own the RFQ
        }

        bid.Status = status;
        bid.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        // Send notification to vendor about bid status update
        try
        {
            var statusText = status == "awarded" ? "accepted" : status;
            var notificationDto = new SendNotificationDto(
                $"🎯 Bid {statusText.ToUpper()}!",
                $"Your bid for '{bid.RFQ.Title}' has been {statusText}.",
                null, // No icon to avoid 404 errors
                null, // No badge to avoid 404 errors
                $"/dashboard?tab=bids&highlight={bid.Id}", // URL for direct navigation
                System.Text.Json.JsonSerializer.Serialize(new { 
                    rfqId = bid.RfqId.ToString(), 
                    bidId = bid.Id.ToString(), 
                    type = "bid_status_update", 
                    status = status,
                    rfqTitle = bid.RFQ.Title
                })
            );

            Console.WriteLine($"Sending bid status notification to vendor {bid.VendorId}");
            await _notificationService.SendNotificationAsync(bid.VendorId, notificationDto);
            Console.WriteLine("Bid status notification sent successfully");
        }
        catch (Exception ex)
        {
            // Log error but don't fail status update
            Console.WriteLine($"Failed to send bid status notification: {ex.Message}");
            Console.WriteLine($"Exception details: {ex}");
        }

        return MapToBidDto(bid);
    }
} 