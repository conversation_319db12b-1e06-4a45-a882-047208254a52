using Microsoft.EntityFrameworkCore;
using BCrypt.Net;
using Bulkit.API.Data;
using Bulkit.API.Models;

namespace Bulkit.API.Extensions;

public static class DatabaseSeeder
{
    public static async Task SeedAsync(BulkitDbContext context)
    {
        // Create notification tables if they don't exist
        await CreateNotificationTablesIfNotExist(context);
        
        // Check if data already exists
        if (await context.Users.AnyAsync())
        {
            return; // Data already seeded
        }

        // Create test users
        var buyer1 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "buyer",
            CompanyName = "Acme Cleaning Services",
            ContactName = "<PERSON>",
            Phone = "+46 8 123 456",
            Address = "Kungsgatan 12",
            City = "Stockholm",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            UpdatedAt = DateTime.UtcNow.AddDays(-30)
        };

        var buyer2 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "buyer",
            CompanyName = "Globe Corporation",
            ContactName = "<PERSON>",
            Phone = "+46 8 789 012",
            Address = "Drottninggatan 25",
            City = "Stockholm",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-25),
            UpdatedAt = DateTime.UtcNow.AddDays(-25)
        };

        var vendor1 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "Nordic Supplies AB",
            ContactName = "Erik Andersson",
            Phone = "+46 31 789 012",
            Address = "Avenyn 45",
            City = "Gothenburg",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-20),
            UpdatedAt = DateTime.UtcNow.AddDays(-20)
        };

        var vendor2 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "EuroTech Solutions",
            ContactName = "Marie Lindqvist",
            Phone = "+46 8 345 678",
            Address = "Vasagatan 10",
            City = "Stockholm",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-15),
            UpdatedAt = DateTime.UtcNow.AddDays(-15)
        };

        var vendor3 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "CleanPro Industries",
            ContactName = "Lars Petersson",
            Phone = "+46 40 567 890",
            Address = "Malmögatan 33",
            City = "Malmö",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-10),
            UpdatedAt = DateTime.UtcNow.AddDays(-10)
        };

        // Create additional vendor users to match frontend data
        var vendor4 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "CleanTech Solutions",
            ContactName = "Anna Nilsson",
            Phone = "+46 8 456 789",
            Address = "Sveavägen 15",
            City = "Stockholm",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-18),
            UpdatedAt = DateTime.UtcNow.AddDays(-18)
        };

        var vendor5 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "Eco Clean Products",
            ContactName = "Peter Johansson",
            Phone = "+46 31 234 567",
            Address = "Götgatan 42",
            City = "Gothenburg",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-12),
            UpdatedAt = DateTime.UtcNow.AddDays(-12)
        };

        var vendor6 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "HygienePlus AB",
            ContactName = "Lisa Karlsson",
            Phone = "+46 40 345 678",
            Address = "Storgatan 20",
            City = "Malmö",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-8),
            UpdatedAt = DateTime.UtcNow.AddDays(-8)
        };

        var vendor7 = new User
        {
            Id = Guid.NewGuid(),
            Email = "<EMAIL>",
            Role = "vendor",
            CompanyName = "Crystal Clean AB",
            ContactName = "Magnus Bergström",
            Phone = "+46 8 567 890",
            Address = "Hamngatan 8",
            City = "Stockholm",
            Country = "Sweden",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            CreatedAt = DateTime.UtcNow.AddDays(-5),
            UpdatedAt = DateTime.UtcNow.AddDays(-5)
        };

        context.Users.AddRange(buyer1, buyer2, vendor1, vendor2, vendor3, vendor4, vendor5, vendor6, vendor7);
        await context.SaveChangesAsync();

        // Create test RFQs
        var rfq1 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            Title = "Industrial Cleaning Supplies for Office Complex",
            Product = "Commercial cleaning supplies",
            Description = "We need a complete set of industrial cleaning supplies for our 5-story office complex. This includes floor cleaners, window cleaners, disinfectants, and related equipment.",
            Quantity = "Monthly supply for 3 months",
            Location = "Stockholm, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(30),
            Budget = 25000,
            Notes = "Eco-friendly products preferred. Must meet EU safety standards.",
            Status = "active",
            Deadline = DateTime.UtcNow.AddDays(14),
            CreatedAt = DateTime.UtcNow.AddDays(-5),
            UpdatedAt = DateTime.UtcNow.AddDays(-5)
        };

        var rfq2 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            Title = "Office Furniture for New Branch",
            Product = "Office furniture",
            Description = "Furniture needed for our new branch office including desks, chairs, cabinets, and meeting room furniture.",
            Quantity = "50 desks, 50 chairs, 20 cabinets, 3 conference tables",
            Location = "Gothenburg, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(45),
            Budget = 150000,
            Notes = "Modern design preferred. Ergonomic chairs required.",
            Status = "active",
            Deadline = DateTime.UtcNow.AddDays(21),
            CreatedAt = DateTime.UtcNow.AddDays(-3),
            UpdatedAt = DateTime.UtcNow.AddDays(-3)
        };

        var rfq3 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer2.Id,
            Title = "IT Equipment Procurement",
            Product = "Computer hardware",
            Description = "Looking for laptops, monitors, and related IT equipment for our growing team.",
            Quantity = "25 laptops, 25 monitors, accessories",
            Location = "Stockholm, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(21),
            Budget = 200000,
            Notes = "Latest generation equipment. 3-year warranty required.",
            Status = "awarded",
            Deadline = DateTime.UtcNow.AddDays(-2),
            CreatedAt = DateTime.UtcNow.AddDays(-10),
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };

        // Create more comprehensive RFQs to match frontend data
        var rfq4 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            Title = "Industrial Floor Cleaners - 500L",
            Product = "Industrial cleaning supplies",
            Description = "High-quality industrial floor cleaners for manufacturing facility. Need eco-friendly formulation that meets Swedish environmental standards.",
            Quantity = "500 liters",
            Location = "Stockholm, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(20),
            Budget = 20000,
            Notes = "Eco-friendly formulation required. Bulk packaging preferred.",
            Status = "active",
            Deadline = DateTime.UtcNow.AddDays(10),
            CreatedAt = DateTime.UtcNow.AddDays(-6),
            UpdatedAt = DateTime.UtcNow.AddDays(-6)
        };

        var rfq5 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            Title = "Disinfectant Spray - 100 units",
            Product = "Disinfectant supplies",
            Description = "Medical-grade disinfectant spray for office spaces. Must meet Swedish health authority standards.",
            Quantity = "100 units",
            Location = "Stockholm, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(15),
            Budget = 12000,
            Notes = "Medical-grade certification required.",
            Status = "awarded",
            Deadline = DateTime.UtcNow.AddDays(-1),
            CreatedAt = DateTime.UtcNow.AddDays(-8),
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };

        var rfq6 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer2.Id,
            Title = "Glass Cleaner - 200L",
            Product = "Glass cleaning supplies",
            Description = "Streak-free glass cleaner for office building windows. Ammonia-free formula preferred.",
            Quantity = "200 liters",
            Location = "Stockholm, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(18),
            Budget = 8000,
            Notes = "Ammonia-free formula. Suitable for office buildings.",
            Status = "closed",
            Deadline = DateTime.UtcNow.AddDays(-3),
            CreatedAt = DateTime.UtcNow.AddDays(-10),
            UpdatedAt = DateTime.UtcNow.AddDays(-2)
        };

        var rfq7 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            Title = "All-Purpose Cleaner - 300L",
            Product = "Multi-surface cleaner",
            Description = "All-purpose cleaner safe for educational facilities. Non-toxic and child-safe formula required.",
            Quantity = "300 liters",
            Location = "Gothenburg, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(25),
            Budget = 15000,
            Notes = "Non-toxic, child-safe formula. Educational facility use.",
            Status = "active",
            Deadline = DateTime.UtcNow.AddDays(12),
            CreatedAt = DateTime.UtcNow.AddDays(-4),
            UpdatedAt = DateTime.UtcNow.AddDays(-4)
        };

        var rfq8 = new RFQ
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer2.Id,
            Title = "Window Cleaning Solution - 150L",
            Product = "Window cleaning supplies",
            Description = "Professional window cleaning solution with anti-streak formula for commercial use.",
            Quantity = "150 liters",
            Location = "Stockholm, Sweden",
            DeliveryDate = DateTime.UtcNow.AddDays(22),
            Budget = 9000,
            Notes = "Anti-streak formula. Commercial grade.",
            Status = "active",
            Deadline = DateTime.UtcNow.AddDays(8),
            CreatedAt = DateTime.UtcNow.AddDays(-3),
            UpdatedAt = DateTime.UtcNow.AddDays(-3)
        };

        context.RFQs.AddRange(rfq1, rfq2, rfq3, rfq4, rfq5, rfq6, rfq7, rfq8);
        await context.SaveChangesAsync();

        // Create comprehensive bids that match frontend data
        // Bids for Industrial Floor Cleaners (rfq4)
        var bid5 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq4.Id,
            VendorId = vendor1.Id, // Nordic Supplies AB
            Price = 15000,
            Currency = "SEK",
            DeliveryTime = "7 days",
            Message = "We offer premium quality industrial floor cleaners with eco-friendly formulation. Fast delivery guaranteed.",
            Status = "pending",
            SubmittedAt = DateTime.UtcNow.AddDays(-1),
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };

        // Bids for Disinfectant Spray (rfq5) - This one is accepted
        var bid6 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq5.Id,
            VendorId = vendor4.Id, // CleanTech Solutions
            Price = 8500,
            Currency = "SEK",
            DeliveryTime = "3 days",
            Message = "Medical-grade disinfectant meeting all Swedish health standards. Bulk packaging available.",
            Status = "awarded",
            SubmittedAt = DateTime.UtcNow.AddDays(-5),
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };

        // Bids for Glass Cleaner (rfq6) - This one is rejected
        var bid7 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq6.Id,
            VendorId = vendor5.Id, // Eco Clean Products
            Price = 6200,
            Currency = "SEK",
            DeliveryTime = "5 days",
            Message = "Streak-free glass cleaner with ammonia-free formula. Perfect for office buildings.",
            Status = "rejected",
            SubmittedAt = DateTime.UtcNow.AddDays(-7),
            UpdatedAt = DateTime.UtcNow.AddDays(-2)
        };

        // Bids for All-Purpose Cleaner (rfq7)
        var bid8 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq7.Id,
            VendorId = vendor6.Id, // HygienePlus AB
            Price = 9200,
            Currency = "SEK",
            DeliveryTime = "10 days",
            Message = "Multi-surface cleaner safe for educational facilities. Non-toxic and child-safe formula.",
            Status = "pending",
            SubmittedAt = DateTime.UtcNow.AddDays(-2),
            UpdatedAt = DateTime.UtcNow.AddDays(-2)
        };

        // Bids for Window Cleaning Solution (rfq8)
        var bid9 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq8.Id,
            VendorId = vendor7.Id, // Crystal Clean AB
            Price = 5800,
            Currency = "SEK",
            DeliveryTime = "4 days",
            Message = "Professional window cleaning solution with anti-streak formula.",
            Status = "pending",
            SubmittedAt = DateTime.UtcNow.AddDays(-1),
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };

        // Additional competing bids for variety
        var bid10 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq4.Id,
            VendorId = vendor4.Id, // CleanTech Solutions competing for floor cleaners
            Price = 16500,
            Currency = "SEK",
            DeliveryTime = "5 days",
            Message = "Premium industrial floor cleaners with enhanced eco-certification. Faster delivery available.",
            Status = "pending",
            SubmittedAt = DateTime.UtcNow.AddHours(-8),
            UpdatedAt = DateTime.UtcNow.AddHours(-8)
        };

        var bid11 = new Bid
        {
            Id = Guid.NewGuid(),
            RfqId = rfq7.Id,
            VendorId = vendor5.Id, // Eco Clean Products competing for all-purpose cleaner
            Price = 8800,
            Currency = "SEK",
            DeliveryTime = "8 days",
            Message = "Eco-friendly all-purpose cleaner specifically designed for educational environments.",
            Status = "pending",
            SubmittedAt = DateTime.UtcNow.AddHours(-12),
            UpdatedAt = DateTime.UtcNow.AddHours(-12)
        };

        context.Bids.AddRange(bid5, bid6, bid7, bid8, bid9, bid10, bid11);
        await context.SaveChangesAsync();

        // Create test messages
        var message1 = new Message
        {
            Id = Guid.NewGuid(),
            RfqId = rfq1.Id,
            SenderId = buyer1.Id,
            ReceiverId = vendor1.Id,
            Content = "Hi Erik, your bid looks interesting. Can you provide more details about the delivery schedule?",
            Timestamp = DateTime.UtcNow.AddHours(-12),
            Read = true
        };

        var message2 = new Message
        {
            Id = Guid.NewGuid(),
            RfqId = rfq1.Id,
            SenderId = vendor1.Id,
            ReceiverId = buyer1.Id,
            Content = "Hello John, thank you for your interest. We can deliver in batches - 50% in week 1 and 50% in week 2. This helps with storage and cash flow.",
            Timestamp = DateTime.UtcNow.AddHours(-10),
            Read = false
        };

        var message3 = new Message
        {
            Id = Guid.NewGuid(),
            RfqId = rfq2.Id,
            SenderId = vendor2.Id,
            ReceiverId = buyer1.Id,
            Content = "We can offer a 5% discount if you're flexible with the delivery timeline. Could extend to 4 weeks instead of 3?",
            Timestamp = DateTime.UtcNow.AddHours(-4),
            Read = false
        };

        context.Messages.AddRange(message1, message2, message3);
        await context.SaveChangesAsync();

        // Create test rating
        var rating1 = new Rating
        {
            Id = Guid.NewGuid(),
            BidId = bid6.Id,
            BuyerId = buyer1.Id,
            VendorId = vendor4.Id,
            Stars = 5,
            Comment = "Excellent service! The equipment was delivered on time and the setup was flawless. Marie and her team were very professional.",
            CreatedAt = DateTime.UtcNow.AddHours(-2)
        };

        context.Ratings.Add(rating1);
        await context.SaveChangesAsync();

        // Create vendor relationships for testing
        var relationship1 = new VendorRelationship
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            VendorId = vendor1.Id,
            Status = "approved",
            CreatedAt = DateTime.UtcNow.AddDays(-15),
            UpdatedAt = DateTime.UtcNow.AddDays(-15)
        };

        var relationship2 = new VendorRelationship
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            VendorId = vendor2.Id,
            Status = "approved",
            CreatedAt = DateTime.UtcNow.AddDays(-12),
            UpdatedAt = DateTime.UtcNow.AddDays(-12)
        };

        var relationship3 = new VendorRelationship
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer1.Id,
            VendorId = vendor4.Id,
            Status = "approved",
            CreatedAt = DateTime.UtcNow.AddDays(-10),
            UpdatedAt = DateTime.UtcNow.AddDays(-10)
        };

        var relationship4 = new VendorRelationship
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer2.Id,
            VendorId = vendor1.Id,
            Status = "approved",
            CreatedAt = DateTime.UtcNow.AddDays(-8),
            UpdatedAt = DateTime.UtcNow.AddDays(-8)
        };

        var relationship5 = new VendorRelationship
        {
            Id = Guid.NewGuid(),
            BuyerId = buyer2.Id,
            VendorId = vendor3.Id,
            Status = "pending",
            CreatedAt = DateTime.UtcNow.AddDays(-5),
            UpdatedAt = DateTime.UtcNow.AddDays(-5)
        };

        context.VendorRelationships.AddRange(relationship1, relationship2, relationship3, relationship4, relationship5);
        await context.SaveChangesAsync();
    }

    private static async Task CreateNotificationTablesIfNotExist(BulkitDbContext context)
    {
        try
        {
            // Force recreate the notification table to ensure correct structure
            Console.WriteLine("Recreating notification table to ensure correct structure...");
            await RecreateNotificationTable(context);

            // Create vendor relationships table
            await context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS `VendorRelationships` (
                    `Id` char(36) COLLATE ascii_general_ci NOT NULL,
                    `BuyerId` char(36) COLLATE ascii_general_ci NOT NULL,
                    `VendorId` char(36) COLLATE ascii_general_ci NOT NULL,
                    `Status` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT 'pending',
                    `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    CONSTRAINT `PK_VendorRelationships` PRIMARY KEY (`Id`),
                    CONSTRAINT `FK_VendorRelationships_Users_BuyerId` FOREIGN KEY (`BuyerId`) REFERENCES `Users` (`Id`) ON DELETE CASCADE,
                    CONSTRAINT `FK_VendorRelationships_Users_VendorId` FOREIGN KEY (`VendorId`) REFERENCES `Users` (`Id`) ON DELETE CASCADE,
                    UNIQUE KEY `IX_VendorRelationships_BuyerId_VendorId` (`BuyerId`, `VendorId`)
                ) CHARACTER SET=utf8mb4;
            ");

            Console.WriteLine("Vendor relationships table created successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating tables: {ex.Message}");
        }
    }

    private static async Task RecreateNotificationTable(BulkitDbContext context)
    {
        try
        {
            // Drop the existing table if it exists
            await context.Database.ExecuteSqlRawAsync("DROP TABLE IF EXISTS `Notifications`");
            Console.WriteLine("Dropped existing Notifications table");
            
            // Recreate the table with the correct structure
            await context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE `Notifications` (
                    `Id` char(36) COLLATE ascii_general_ci NOT NULL,
                    `UserId` char(36) COLLATE ascii_general_ci NOT NULL,
                    `Endpoint` varchar(500) CHARACTER SET utf8mb4 NULL,
                    `P256DH` varchar(255) CHARACTER SET utf8mb4 NULL,
                    `Auth` varchar(255) CHARACTER SET utf8mb4 NULL,
                    `UserAgent` varchar(500) CHARACTER SET utf8mb4 NULL,
                    `IsActive` tinyint(1) NOT NULL DEFAULT 1,
                    `Title` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
                    `Body` text CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
                    `Icon` varchar(500) CHARACTER SET utf8mb4 NULL,
                    `Badge` varchar(500) CHARACTER SET utf8mb4 NULL,
                    `Url` varchar(500) CHARACTER SET utf8mb4 NULL,
                    `Data` text CHARACTER SET utf8mb4 NULL,
                    `IsSent` tinyint(1) NOT NULL DEFAULT 0,
                    `IsRead` tinyint(1) NOT NULL DEFAULT 0,
                    `Type` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT 'general',
                    `CreatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `UpdatedAt` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    `SentAt` datetime(6) NULL,
                    `ReadAt` datetime(6) NULL,
                    CONSTRAINT `PK_Notifications` PRIMARY KEY (`Id`),
                    CONSTRAINT `FK_Notifications_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `Users` (`Id`) ON DELETE CASCADE,
                    UNIQUE KEY `IX_Notifications_UserId_Endpoint` (`UserId`, `Endpoint`)
                ) CHARACTER SET=utf8mb4;
            ");

            Console.WriteLine("Notification table recreated successfully with all required columns");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error recreating notification table: {ex.Message}");
            throw;
        }
    }

    private static async Task AddMissingNotificationColumns(BulkitDbContext context)
    {
        try
        {
            // Add missing columns one by one with error handling
            var columnsToAdd = new[]
            {
                "ADD COLUMN IF NOT EXISTS `Title` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT ''",
                "ADD COLUMN IF NOT EXISTS `Body` text CHARACTER SET utf8mb4 NOT NULL DEFAULT ''",
                "ADD COLUMN IF NOT EXISTS `Badge` varchar(500) CHARACTER SET utf8mb4 NULL",
                "ADD COLUMN IF NOT EXISTS `Icon` varchar(500) CHARACTER SET utf8mb4 NULL",
                "ADD COLUMN IF NOT EXISTS `Url` varchar(500) CHARACTER SET utf8mb4 NULL",
                "ADD COLUMN IF NOT EXISTS `Data` text CHARACTER SET utf8mb4 NULL",
                "ADD COLUMN IF NOT EXISTS `IsSent` tinyint(1) NOT NULL DEFAULT 0",
                "ADD COLUMN IF NOT EXISTS `IsRead` tinyint(1) NOT NULL DEFAULT 0",
                "ADD COLUMN IF NOT EXISTS `SentAt` datetime(6) NULL",
                "ADD COLUMN IF NOT EXISTS `ReadAt` datetime(6) NULL",
                "ADD COLUMN IF NOT EXISTS `Type` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT 'general'"
            };

            foreach (var columnDef in columnsToAdd)
            {
                try
                {
                    await context.Database.ExecuteSqlRawAsync($"ALTER TABLE Notifications {columnDef}");
                    Console.WriteLine($"Added column: {columnDef}");
                }
                catch (Exception ex)
                {
                    // Column might already exist, which is fine
                    Console.WriteLine($"Column might already exist or error adding: {ex.Message}");
                }
            }

            Console.WriteLine("Notification table schema updated successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating notification table schema: {ex.Message}");
        }
    }
} 