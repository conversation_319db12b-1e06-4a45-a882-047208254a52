using Microsoft.EntityFrameworkCore;
using Bulkit.API.Models;

namespace Bulkit.API.Data;

public class BulkitDbContext : DbContext
{
    public BulkitDbContext(DbContextOptions<BulkitDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<RFQ> RFQs { get; set; }
    public DbSet<Bid> Bids { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<Rating> Ratings { get; set; }
    public DbSet<Notification> Notifications { get; set; }
    public DbSet<VendorRelationship> VendorRelationships { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // User configuration
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Email).IsUnique();
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        });

        // RFQ configuration
        modelBuilder.Entity<RFQ>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            
            entity.HasOne(e => e.Buyer)
                .WithMany(u => u.RFQs)
                .HasForeignKey(e => e.BuyerId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Bid configuration
        modelBuilder.Entity<Bid>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SubmittedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            
            entity.HasOne(e => e.RFQ)
                .WithMany(r => r.Bids)
                .HasForeignKey(e => e.RfqId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Vendor)
                .WithMany(u => u.Bids)
                .HasForeignKey(e => e.VendorId)
                .OnDelete(DeleteBehavior.Restrict);

            // Ensure a vendor can only bid once per RFQ
            entity.HasIndex(e => new { e.RfqId, e.VendorId }).IsUnique();
        });

        // Message configuration
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Timestamp).HasDefaultValueSql("CURRENT_TIMESTAMP");
            
            entity.HasOne(e => e.RFQ)
                .WithMany(r => r.Messages)
                .HasForeignKey(e => e.RfqId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Sender)
                .WithMany(u => u.SentMessages)
                .HasForeignKey(e => e.SenderId)
                .OnDelete(DeleteBehavior.Restrict);
                
            entity.HasOne(e => e.Receiver)
                .WithMany(u => u.ReceivedMessages)
                .HasForeignKey(e => e.ReceiverId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Rating configuration
        modelBuilder.Entity<Rating>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            
            entity.HasOne(e => e.Bid)
                .WithMany(b => b.Ratings)
                .HasForeignKey(e => e.BidId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Buyer)
                .WithMany(u => u.RatingsGiven)
                .HasForeignKey(e => e.BuyerId)
                .OnDelete(DeleteBehavior.Restrict);
                
            entity.HasOne(e => e.Vendor)
                .WithMany(u => u.RatingsReceived)
                .HasForeignKey(e => e.VendorId)
                .OnDelete(DeleteBehavior.Restrict);

            // Ensure a buyer can only rate a bid once
            entity.HasIndex(e => new { e.BidId, e.BuyerId }).IsUnique();
        });

        // Notification configuration (unified table for subscriptions and messages)
        modelBuilder.Entity<Notification>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            
            entity.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Ensure unique subscription per endpoint per user (only for subscription records)
            entity.HasIndex(e => new { e.UserId, e.Endpoint }).IsUnique();
        });

        // Vendor relationship configuration
        modelBuilder.Entity<VendorRelationship>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            
            entity.HasOne(e => e.Buyer)
                .WithMany(u => u.VendorRelationships)
                .HasForeignKey(e => e.BuyerId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Vendor)
                .WithMany(u => u.BuyerRelationships)
                .HasForeignKey(e => e.VendorId)
                .OnDelete(DeleteBehavior.Cascade);

            // Ensure unique relationship between buyer and vendor
            entity.HasIndex(e => new { e.BuyerId, e.VendorId }).IsUnique();
        });
    }
} 