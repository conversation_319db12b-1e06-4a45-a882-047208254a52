{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=127.0.0.1;Port=3307;Database=bulkit_dev_db;Uid=bulkit_user;Pwd=****************;Allow User Variables=true;"}, "JwtSettings": {"SecretKey": "development-jwt-secret-key-for-local-testing-only-not-for-production-use", "Issuer": "Bulkit.API.Dev", "Audience": "Bulkit.Client.Dev", "ExpiryInDays": 30}, "Notifications": {"VapidPublicKey": "BEl62iUYgUivxIkv69yViEuiBIa1HI0lKb6b4Kt8cqQvIzhO05jcrYb6xp-idEmMjyIxr0JhkbaTWLtFmV21XZgY", "VapidPrivateKey": "VkE8HoVQfoV1OAmXK8UpJB5QlKgVoQHm7E7nws8wpS4", "VapidSubject": "mailto:<EMAIL>"}}