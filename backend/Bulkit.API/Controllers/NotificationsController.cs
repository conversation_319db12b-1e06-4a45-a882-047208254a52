using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using Bulkit.API.DTOs;
using Bulkit.API.Services;
using Bulkit.API.Data;

namespace Bulkit.API.Controllers;

[ApiController]
[Route("[controller]")]
[Authorize]
public class NotificationsController : ControllerBase
{
    private readonly INotificationService _notificationService;
    private readonly BulkitDbContext _context;

    public NotificationsController(INotificationService notificationService, BulkitDbContext context)
    {
        _notificationService = notificationService;
        _context = context;
    }

    [HttpGet("vapid-keys")]
    [AllowAnonymous]
    public async Task<ActionResult<VapidKeysDto>> GetVapidKeys()
    {
        var keys = await _notificationService.GetVapidKeysAsync();
        return Ok(new { publicKey = keys.PublicKey });
    }

    [HttpPost("subscribe")]
    public async Task<ActionResult<NotificationSubscriptionDto>> CreateSubscription([FromBody] CreateNotificationSubscriptionDto subscriptionDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            var subscription = await _notificationService.CreateSubscriptionAsync(userId, subscriptionDto);
            return Ok(subscription);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("subscribe")]
    public async Task<ActionResult> DeleteSubscription([FromQuery] string endpoint)
    {
        try
        {
            var userId = GetCurrentUserId();
            var success = await _notificationService.DeleteSubscriptionAsync(userId, endpoint);
            
            if (!success)
            {
                return NotFound(new { message = "Subscription not found" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("subscriptions")]
    public async Task<ActionResult<IEnumerable<NotificationSubscriptionDto>>> GetSubscriptions()
    {
        var userId = GetCurrentUserId();
        var subscriptions = await _notificationService.GetUserSubscriptionsAsync(userId);
        return Ok(subscriptions);
    }

    [HttpPost("send")]
    [Authorize(Policy = "AdminOnly")]
    public async Task<ActionResult<NotificationMessageDto>> SendNotification([FromBody] SendNotificationDto notificationDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            var notification = await _notificationService.SendNotificationAsync(userId, notificationDto);
            return Ok(notification);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("send-to-vendors")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult> SendNotificationToVendors([FromBody] SendNotificationDto notificationDto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var success = await _notificationService.SendNotificationToVendorsAsync(notificationDto, buyerId);
            
            if (!success)
            {
                return BadRequest(new { message = "Failed to send notifications" });
            }

            return Ok(new { message = "Notifications sent successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("messages")]
    public async Task<ActionResult<IEnumerable<NotificationMessageDto>>> GetNotifications()
    {
        var userId = GetCurrentUserId();
        var notifications = await _notificationService.GetUserNotificationsAsync(userId);
        return Ok(notifications);
    }

    [HttpPut("messages/{id}/read")]
    public async Task<ActionResult> MarkAsRead(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var success = await _notificationService.MarkNotificationAsReadAsync(id, userId);
            
            if (!success)
            {
                return NotFound(new { message = "Notification not found" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("messages/{id}")]
    public async Task<ActionResult> DeleteNotification(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId && n.Type == "message");

            if (notification == null)
            {
                return NotFound(new { message = "Notification not found" });
            }

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("messages/clear-all")]
    public async Task<ActionResult> ClearAllNotifications()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userNotifications = await _context.Notifications
                .Where(n => n.UserId == userId && n.Type == "message")
                .ToListAsync();

            _context.Notifications.RemoveRange(userNotifications);
            await _context.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim!);
    }
} 