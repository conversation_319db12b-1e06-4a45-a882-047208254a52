using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Bulkit.API.DTOs;
using Bulkit.API.Services;

namespace Bulkit.API.Controllers;

[ApiController]
[Route("[controller]")]
[Authorize]
public class RfqsController : ControllerBase
{
    private readonly IRfqService _rfqService;
    private readonly IBidService _bidService;
    private readonly IMessageService _messageService;

    public RfqsController(IRfqService rfqService, IBidService bidService, IMessageService messageService)
    {
        _rfqService = rfqService;
        _bidService = bidService;
        _messageService = messageService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<RfqSummaryDto>>> GetRfqs()
    {
        var userId = GetCurrentUserId();
        var userRole = GetCurrentUserRole();
        
        var rfqs = await _rfqService.GetRfqsForUserAsync(userId, userRole);
        return Ok(rfqs);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<RfqDto>> GetRfq(Guid id)
    {
        var userId = GetCurrentUserId();
        var rfq = await _rfqService.GetRfqByIdAsync(id, userId);
        
        if (rfq == null)
        {
            return NotFound(new { message = "RFQ not found" });
        }

        return Ok(rfq);
    }

    [HttpPost]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<RfqDto>> CreateRfq([FromBody] CreateRfqDto createRfqDto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var rfq = await _rfqService.CreateRfqAsync(createRfqDto, buyerId);
            return CreatedAtAction(nameof(GetRfq), new { id = rfq.Id }, rfq);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPut("{id}")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<RfqDto>> UpdateRfq(Guid id, [FromBody] UpdateRfqDto updateRfqDto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var rfq = await _rfqService.UpdateRfqAsync(id, updateRfqDto, buyerId);
            
            if (rfq == null)
            {
                return NotFound(new { message = "RFQ not found or you don't have permission to update it" });
            }

            return Ok(rfq);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult> DeleteRfq(Guid id)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var success = await _rfqService.DeleteRfqAsync(id, buyerId);
            
            if (!success)
            {
                return NotFound(new { message = "RFQ not found or you don't have permission to delete it" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/can-edit")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<bool>> CanEditRfq(Guid id)
    {
        var buyerId = GetCurrentUserId();
        var canEdit = await _rfqService.CanEditRfqAsync(id, buyerId);
        return Ok(canEdit);
    }

    [HttpGet("{id}/can-delete")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<bool>> CanDeleteRfq(Guid id)
    {
        var buyerId = GetCurrentUserId();
        var canDelete = await _rfqService.CanDeleteRfqAsync(id, buyerId);
        return Ok(canDelete);
    }

    [HttpGet("{id}/bids")]
    public async Task<ActionResult<IEnumerable<BidDto>>> GetBidsForRfq(Guid id)
    {
        var bids = await _bidService.GetBidsForRfqAsync(id);
        return Ok(bids);
    }

    [HttpPost("{id}/bids")]
    [Authorize(Policy = "VendorOnly")]
    public async Task<ActionResult<BidDto>> CreateBid(Guid id, [FromBody] CreateBidDto createBidDto)
    {
        try
        {
            var vendorId = GetCurrentUserId();
            var bid = await _bidService.CreateBidAsync(id, createBidDto, vendorId);
            return CreatedAtAction("GetBid", "Bids", new { id = bid.Id }, bid);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/award")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<RfqDto>> AwardBid(Guid id, [FromBody] AwardBidDto awardBidDto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var bidId = Guid.Parse(awardBidDto.BidId);
            var rfq = await _rfqService.AwardBidAsync(id, bidId, buyerId);
            
            if (rfq == null)
            {
                return NotFound(new { message = "RFQ or bid not found" });
            }

            return Ok(rfq);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}/messages")]
    public async Task<ActionResult<IEnumerable<MessageDto>>> GetMessages(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var messages = await _messageService.GetMessagesForRfqAsync(id, userId);
            return Ok(messages);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/messages")]
    public async Task<ActionResult<MessageDto>> SendMessage(Guid id, [FromBody] SendMessageDto sendMessageDto)
    {
        try
        {
            var senderId = GetCurrentUserId();
            var message = await _messageService.SendMessageAsync(id, sendMessageDto, senderId);
            return CreatedAtAction("GetMessages", new { id }, message);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim!);
    }

    private string GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value!;
    }
} 