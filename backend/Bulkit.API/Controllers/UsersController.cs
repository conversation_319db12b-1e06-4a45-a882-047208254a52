using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Bulkit.API.DTOs;
using Bulkit.API.Services;

namespace Bulkit.API.Controllers;

[ApiController]
[Route("[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly IRatingService _ratingService;

    public UsersController(IAuthService authService, IRatingService ratingService)
    {
        _authService = authService;
        _ratingService = ratingService;
    }

    [HttpGet("me")]
    public async Task<ActionResult<UserDto>> GetCurrentUser()
    {
        var userId = GetCurrentUserId();
        var user = await _authService.GetUserByIdAsync(userId);
        
        if (user == null)
        {
            return NotFound(new { message = "User not found" });
        }

        var userDto = new UserDto(
            user.Id.ToString(),
            user.Email,
            user.Role,
            user.CompanyName,
            user.ContactName,
            user.Phone,
            user.Address,
            user.City,
            user.Country,
            user.CreatedAt,
            user.UpdatedAt
        );

        return Ok(userDto);
    }

    [HttpGet("{id}/ratings")]
    public async Task<ActionResult<IEnumerable<RatingDto>>> GetUserRatings(Guid id)
    {
        var ratings = await _ratingService.GetRatingsForVendorAsync(id);
        return Ok(ratings);
    }

    [HttpGet("{id}/average-rating")]
    public async Task<ActionResult<object>> GetUserAverageRating(Guid id)
    {
        var averageRating = await _ratingService.GetAverageRatingForVendorAsync(id);
        return Ok(new { vendorId = id, averageRating, totalRatings = (await _ratingService.GetRatingsForVendorAsync(id)).Count() });
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim!);
    }
} 