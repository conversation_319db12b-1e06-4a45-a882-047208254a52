using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Bulkit.API.DTOs;
using Bulkit.API.Services;

namespace Bulkit.API.Controllers;

[ApiController]
[Route("[controller]")]
[Authorize(Policy = "BuyerOnly")]
public class VendorRelationshipsController : ControllerBase
{
    private readonly IVendorRelationshipService _vendorRelationshipService;

    public VendorRelationshipsController(IVendorRelationshipService vendorRelationshipService)
    {
        _vendorRelationshipService = vendorRelationshipService;
    }

    [HttpGet("vendors")]
    public async Task<ActionResult<IEnumerable<VendorListDto>>> GetVendorList()
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var vendors = await _vendorRelationshipService.GetVendorListAsync(buyerId);
            return Ok(vendors);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("vendors")]
    public async Task<ActionResult<VendorRelationshipDto>> AddVendor([FromBody] CreateVendorRelationshipDto dto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var relationship = await _vendorRelationshipService.CreateRelationshipAsync(buyerId, dto);
            return Ok(relationship);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPut("vendors/{vendorId}")]
    public async Task<ActionResult<VendorRelationshipDto>> UpdateVendorStatus(Guid vendorId, [FromBody] UpdateVendorRelationshipDto dto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var relationship = await _vendorRelationshipService.UpdateRelationshipAsync(buyerId, vendorId, dto);
            return Ok(relationship);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("vendors/{vendorId}")]
    public async Task<ActionResult> RemoveVendor(Guid vendorId)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var success = await _vendorRelationshipService.DeleteRelationshipAsync(buyerId, vendorId);
            
            if (!success)
            {
                return NotFound(new { message = "Vendor relationship not found" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim!);
    }
} 