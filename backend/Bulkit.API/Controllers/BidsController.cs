using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Bulkit.API.DTOs;
using Bulkit.API.Services;

namespace Bulkit.API.Controllers;

[ApiController]
[Route("[controller]")]
[Authorize]
public class BidsController : ControllerBase
{
    private readonly IBidService _bidService;
    private readonly IRatingService _ratingService;

    public BidsController(IBidService bidService, IRatingService ratingService)
    {
        _bidService = bidService;
        _ratingService = ratingService;
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<BidDto>> GetBid(Guid id)
    {
        var bid = await _bidService.GetBidByIdAsync(id);
        
        if (bid == null)
        {
            return NotFound(new { message = "Bid not found" });
        }

        return Ok(bid);
    }

    [HttpPut("{id}")]
    [Authorize(Policy = "VendorOnly")]
    public async Task<ActionResult<BidDto>> UpdateBid(Guid id, [FromBody] UpdateBidDto updateBidDto)
    {
        try
        {
            var vendorId = GetCurrentUserId();
            var bid = await _bidService.UpdateBidAsync(id, updateBidDto, vendorId);
            
            if (bid == null)
            {
                return NotFound(new { message = "Bid not found or you don't have permission to update it" });
            }

            return Ok(bid);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = "VendorOnly")]
    public async Task<ActionResult> DeleteBid(Guid id)
    {
        try
        {
            var vendorId = GetCurrentUserId();
            var success = await _bidService.DeleteBidAsync(id, vendorId);
            
            if (!success)
            {
                return NotFound(new { message = "Bid not found or you don't have permission to delete it" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("my-bids")]
    [Authorize(Policy = "VendorOnly")]
    public async Task<ActionResult<IEnumerable<BidDto>>> GetMyBids()
    {
        var vendorId = GetCurrentUserId();
        var bids = await _bidService.GetBidsForVendorAsync(vendorId);
        return Ok(bids);
    }

    [HttpGet("for-buyer")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<IEnumerable<BidDto>>> GetBidsForBuyer()
    {
        var buyerId = GetCurrentUserId();
        var bids = await _bidService.GetBidsForBuyerAsync(buyerId);
        return Ok(bids);
    }

    [HttpPut("{id}/status")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<BidDto>> UpdateBidStatus(Guid id, [FromBody] UpdateBidStatusDto updateStatusDto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var bid = await _bidService.UpdateBidStatusAsync(id, updateStatusDto.Status, buyerId);
            
            if (bid == null)
            {
                return NotFound(new { message = "Bid not found or you don't have permission to update it" });
            }

            return Ok(bid);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/rate")]
    [Authorize(Policy = "BuyerOnly")]
    public async Task<ActionResult<RatingDto>> RateBid(Guid id, [FromBody] CreateRatingDto createRatingDto)
    {
        try
        {
            var buyerId = GetCurrentUserId();
            var rating = await _ratingService.CreateRatingAsync(id, createRatingDto, buyerId);
            return CreatedAtAction("GetRating", "Ratings", new { id = rating.Id }, rating);
        }
        catch (UnauthorizedAccessException ex)
        {
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim!);
    }
} 