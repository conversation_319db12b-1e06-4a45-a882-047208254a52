using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.DTOs;

public record VendorRelationshipDto(
    string Id,
    string BuyerId,
    string VendorId,
    string Status,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    UserDto Buyer,
    UserDto Vendor
);

public record CreateVendorRelationshipDto(
    [Required] string VendorId
);

public record UpdateVendorRelationshipDto(
    [Required] string Status
);

public record VendorListDto(
    string Id,
    string CompanyName,
    string ContactName,
    string Email,
    string Phone,
    string City,
    string Country,
    string Status,
    DateTime CreatedAt
); 