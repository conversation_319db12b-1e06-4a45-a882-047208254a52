using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.DTOs;

public record RegisterDto(
    [Required][EmailAddress] string Email,
    [Required][MinLength(6)] string Password,
    [Required] string Role,
    [Required] string CompanyName,
    [Required] string ContactName,
    string? Phone,
    string? Address,
    [Required] string City,
    [Required] string Country
);

public record LoginDto(
    [Required][EmailAddress] string Email,
    [Required] string Password
);

public record AuthResponseDto(
    string Token,
    UserDto User
);

public record UserDto(
    string Id,
    string Email,
    string Role,
    string CompanyName,
    string ContactName,
    string? Phone,
    string? Address,
    string City,
    string Country,
    DateTime CreatedAt,
    DateTime UpdatedAt
); 