using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.DTOs;

public record SendMessageDto(
    [Required] string ReceiverId,
    [Required][MaxLength(2000)] string Content
);

public record MessageDto(
    string Id,
    string RfqId,
    string SenderId,
    string ReceiverId,
    string Content,
    DateTime Timestamp,
    bool Read,
    UserDto Sender,
    UserDto Receiver
);

public record MarkAsReadDto(
    [Required] string MessageId
); 