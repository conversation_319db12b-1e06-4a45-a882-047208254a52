using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.DTOs;

public record CreateBidDto(
    [Required][Range(0.01, double.MaxValue)] decimal Price,
    [Required] string Currency,
    [Required] string DeliveryTime,
    [Required] string Message
);

public record UpdateBidDto(
    decimal? Price,
    string? Currency,
    string? DeliveryTime,
    string? Message,
    string? Status
);

public record BidDto(
    string Id,
    string RfqId,
    string VendorId,
    decimal Price,
    string Currency,
    string DeliveryTime,
    string Message,
    string Status,
    DateTime SubmittedAt,
    DateTime UpdatedAt,
    UserDto Vendor,
    List<RatingDto> Ratings
);

public record BidSummaryDto(
    string Id,
    string RfqId,
    string VendorId,
    decimal Price,
    string Currency,
    string DeliveryTime,
    string Status,
    DateTime SubmittedAt,
    string VendorCompanyName
);

public record AwardBidDto(
    [Required] string BidId
);

public record UpdateBidStatusDto(
    [Required] string Status
); 