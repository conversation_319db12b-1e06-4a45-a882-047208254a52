using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.DTOs;

public record CreateRfqDto(
    [Required] string Title,
    [Required] string Product,
    [Required] string Description,
    [Required] string Quantity,
    [Required] string Location,
    [Required] DateTime DeliveryDate,
    decimal? Budget,
    string? Notes,
    [Required] DateTime Deadline
);

public record UpdateRfqDto(
    string? Title,
    string? Product,
    string? Description,
    string? Quantity,
    string? Location,
    DateTime? DeliveryDate,
    decimal? Budget,
    string? Notes,
    DateTime? Deadline,
    string? Status
);

public record RfqDto(
    string Id,
    string BuyerId,
    string Title,
    string Product,
    string Description,
    string Quantity,
    string Location,
    DateTime DeliveryDate,
    decimal? Budget,
    string? Notes,
    string Status,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    DateTime Deadline,
    UserDto Buyer,
    List<BidDto> Bids,
    int BidCount
);

public record RfqSummaryDto(
    string Id,
    string BuyerId,
    string Title,
    string Product,
    string Quantity,
    string Location,
    DateTime DeliveryDate,
    decimal? Budget,
    string Status,
    DateTime CreatedAt,
    DateTime Deadline,
    int BidCount
); 