using System.ComponentModel.DataAnnotations;

namespace Bulkit.API.DTOs;

public record CreateNotificationSubscriptionDto(
    [Required] string Endpoint,
    [Required] string P256DH,
    [Required] string Auth,
    string? UserAgent
);

public record NotificationSubscriptionDto(
    string Id,
    string UserId,
    string Endpoint,
    bool IsActive,
    DateTime CreatedAt
);

public record SendNotificationDto(
    [Required] string Title,
    [Required] string Body,
    string? Icon,
    string? Badge,
    string? Url,
    string? Data
);

public record NotificationMessageDto(
    string Id,
    string UserId,
    string Title,
    string Body,
    string? Icon,
    string? Badge,
    string? Url,
    string? Data,
    bool IsSent,
    bool IsRead,
    DateTime CreatedAt,
    DateTime? SentAt,
    DateTime? ReadAt
);

public record VapidKeysDto(
    string PublicKey,
    string PrivateKey
); 