import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // Custom Bulkit Design System
      colors: {
        // Bulkit Brand Colors
        bulkit: {
          primary: "#2563eb", // Blue-600
          secondary: "#059669", // Emerald-600
          accent: "#7c3aed", // Violet-600
          success: "#16a34a", // Green-600
          warning: "#ea580c", // Orange-600
          danger: "#dc2626", // Red-600
        },
        // Enhanced Color Palette
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },

      // Custom Background Gradients
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "bulkit-primary": "linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)",
        "bulkit-secondary": "linear-gradient(135deg, #059669 0%, #10b981 100%)",
        "bulkit-accent": "linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)",
        "bulkit-warm": "linear-gradient(135deg, #f59e0b 0%, #f97316 100%)",
        "bulkit-cool": "linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%)",
        glass:
          "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)",
      },

      // Enhanced Border Radius
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        xl: "1rem",
        "2xl": "1.5rem",
        "3xl": "2rem",
      },

      // Custom Shadows
      boxShadow: {
        "bulkit-sm": "0 2px 4px rgba(37, 99, 235, 0.1)",
        "bulkit-md": "0 4px 6px rgba(37, 99, 235, 0.15)",
        "bulkit-lg": "0 10px 15px rgba(37, 99, 235, 0.2)",
        "bulkit-xl": "0 20px 25px rgba(37, 99, 235, 0.25)",
        glass: "0 8px 32px rgba(31, 38, 135, 0.37)",
      },

      // Custom Animations
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "fade-in": {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "slide-in": {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(0)" },
        },
        "pulse-slow": {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.5" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.5s ease-out",
        "slide-in": "slide-in 0.3s ease-out",
        "pulse-slow": "pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },

      // Custom Typography
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
        mono: ["JetBrains Mono", "monospace"],
      },

      // Custom Spacing
      spacing: {
        "18": "4.5rem",
        "88": "22rem",
        "100": "25rem",
        "112": "28rem",
        "128": "32rem",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    // Add custom utilities
    function ({ addUtilities }: any) {
      const newUtilities = {
        ".text-gradient": {
          background: "linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)",
          "-webkit-background-clip": "text",
          "background-clip": "text",
          "-webkit-text-fill-color": "transparent",
        },
        ".glass": {
          background: "rgba(255, 255, 255, 0.1)",
          "backdrop-filter": "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        },
        ".card-hover": {
          transition: "all 0.3s ease",
          "&:hover": {
            transform: "translateY(-4px)",
            "box-shadow": "0 20px 25px rgba(37, 99, 235, 0.25)",
          },
        },
      };
      addUtilities(newUtilities);
    },
  ],
};
export default config;
