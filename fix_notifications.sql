-- Add missing columns to notifications table if they don't exist
ALTER TABLE notifications
ADD COLUMN IF NOT EXISTS Title varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS Body text CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS Icon varchar(255) CHARACTER SET utf8mb4 NULL,
ADD COLUMN IF NOT EXISTS Badge varchar(255) CHARACTER SET utf8mb4 NULL,
ADD COLUMN IF NOT EXISTS Url varchar(255) CHARACTER SET utf8mb4 NULL,
ADD COLUMN IF NOT EXISTS Data text CHARACTER SET utf8mb4 NULL,
ADD COLUMN IF NOT EXISTS IsSent tinyint(1) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS IsRead tinyint(1) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS Type varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT 'general',
ADD COLUMN IF NOT EXISTS CreatedAt datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS UpdatedAt datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS SentAt datetime(6) NULL,
ADD COLUMN IF NOT EXISTS ReadAt datetime(6) NULL; 