# Notification System Test Guide

## 🔍 **Current Issue: Wrong Notification Recipients**

**Problem**: Buyer receives "New Bid Received" notification when creating an RFQ instead of vendors receiving "New RFQ Available" notifications.

## Step-by-Step Testing

### 1. Test Backend Notification Endpoints

```bash
# Test VAPID keys endpoint
curl -X GET http://localhost:5001/notifications/vapid-keys

# Expected response: {"publicKey":"..."}
```

### 2. Test Vendor Login and Notification Setup

1. **Login as vendor:** `<EMAIL>` / `password123`
2. **Go to Profile section**
3. **Enable notifications** in Notification Settings
4. **Check browser permissions** - allow notifications
5. **Look for debug info** in the notification bell (development mode)

### 3. Test RFQ Creation Flow

1. **Login as buyer:** `<EMAIL>` / `password123`
2. **Create a new RFQ** (e.g., "Test RFQ for Notifications")
3. **Check backend logs** for notification sending:
   ```
   Sending notification to X vendors: 🔔 New RFQ Available!
   Sending notification to vendor: <EMAIL> (vendor-id)
   Successfully sent notification to vendor: <EMAIL>
   ```
4. **Switch to vendor account** and check notifications
5. **Vendor should see**: "🔔 New RFQ Available!" notification

### 4. Test Bid Creation Flow

1. **Login as vendor:** `<EMAIL>` / `password123`
2. **Submit a bid** on an existing RFQ
3. **Check backend logs** for notification sending:
   ```
   SendNotificationAsync called for user [buyer-id]
   Created notification message record: [notification-id]
   Found X active subscriptions for user [buyer-id]
   ```
4. **Switch to buyer account** and check notifications
5. **Buyer should see**: "💰 New Bid Received!" notification

### 5. Manual Test Notification

1. **As vendor, click the notification bell**
2. **Click "Test" button** to send a test notification
3. **Check if notification appears**

### 6. Check Database

```sql
-- Check if notifications exist for vendor
SELECT * FROM NotificationMessages WHERE UserId = 'vendor-user-id' ORDER BY CreatedAt DESC;

-- Check if notifications exist for buyer
SELECT * FROM NotificationMessages WHERE UserId = 'buyer-user-id' ORDER BY CreatedAt DESC;

-- Check if vendor has subscriptions
SELECT * FROM Notifications WHERE UserId = 'vendor-user-id';
```

## 🔧 **Debugging Steps**

### Check Backend Logs

Look for these specific log messages:

**For RFQ Creation:**

```
Sending notification to X vendors: 🔔 New RFQ Available!
Sending notification to vendor: <EMAIL> (vendor-id)
Successfully sent notification to vendor: <EMAIL>
```

**For Bid Creation:**

```
SendNotificationAsync called for user [buyer-id]
Created notification message record: [notification-id]
Found X active subscriptions for user [buyer-id]
```

### Check Frontend Console

Open browser console (F12) and look for:

```
Loading notifications for user: [user-id]
Notification response status: 200
Loaded notifications: [array of notifications]
```

## Common Issues

1. **Vendor hasn't enabled notifications** - Most common issue
2. **Browser blocking notifications** - Check browser settings
3. **Service worker not registered** - Check browser console
4. **VAPID keys not working** - Check backend logs
5. **Database connection issues** - Check backend logs
6. **Wrong notification recipients** - Check notification service logic

## Debug Steps

1. **Check browser console** for JavaScript errors
2. **Check backend logs** for notification errors
3. **Verify database** has notification records
4. **Test with different browsers** (Chrome, Firefox, Edge)
5. **Check network tab** for failed API calls
